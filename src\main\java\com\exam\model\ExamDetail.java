package com.exam.model;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 考试详情实体类
 * 记录每道题的答题情况
 * 实现MVC模式中的Model层
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ExamDetail implements Serializable {
    private static final long serialVersionUID = 1L;
    
    // 考试详情属性
    private Integer detailId;           // 详情编号（主键）
    private Integer recordId;           // 考试记录编号（外键）
    private Integer questionId;         // 题目编号（外键）
    private Question.Answer userAnswer; // 用户答案
    private Question.Answer correctAnswer; // 正确答案
    private Boolean isCorrect;          // 是否正确
    private Timestamp answerTime;       // 答题时间
    
    // 关联的题目对象（用于显示题目详情）
    private Question question;
    
    // 默认构造函数
    public ExamDetail() {
        this.isCorrect = false;
        this.answerTime = new Timestamp(System.currentTimeMillis());
    }
    
    // 带参构造函数
    public ExamDetail(Integer recordId, Integer questionId, 
                     Question.Answer userAnswer, Question.Answer correctAnswer) {
        this();
        this.recordId = recordId;
        this.questionId = questionId;
        this.userAnswer = userAnswer;
        this.correctAnswer = correctAnswer;
        this.isCorrect = (userAnswer != null && userAnswer.equals(correctAnswer));
    }
    
    // 完整构造函数
    public ExamDetail(Integer recordId, Question question, Question.Answer userAnswer) {
        this();
        this.recordId = recordId;
        this.question = question;
        this.questionId = question.getQuestionId();
        this.userAnswer = userAnswer;
        this.correctAnswer = question.getCorrectAnswer();
        this.isCorrect = (userAnswer != null && userAnswer.equals(correctAnswer));
    }
    
    // Getter和Setter方法
    public Integer getDetailId() {
        return detailId;
    }
    
    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }
    
    public Integer getRecordId() {
        return recordId;
    }
    
    public void setRecordId(Integer recordId) {
        this.recordId = recordId;
    }
    
    public Integer getQuestionId() {
        return questionId;
    }
    
    public void setQuestionId(Integer questionId) {
        this.questionId = questionId;
    }
    
    public Question.Answer getUserAnswer() {
        return userAnswer;
    }
    
    public void setUserAnswer(Question.Answer userAnswer) {
        this.userAnswer = userAnswer;
        // 重新计算是否正确
        this.isCorrect = (userAnswer != null && userAnswer.equals(correctAnswer));
    }
    
    public Question.Answer getCorrectAnswer() {
        return correctAnswer;
    }
    
    public void setCorrectAnswer(Question.Answer correctAnswer) {
        this.correctAnswer = correctAnswer;
        // 重新计算是否正确
        this.isCorrect = (userAnswer != null && userAnswer.equals(correctAnswer));
    }
    
    public Boolean isCorrect() {
        return isCorrect;
    }
    
    public void setCorrect(Boolean correct) {
        isCorrect = correct;
    }
    
    public Timestamp getAnswerTime() {
        return answerTime;
    }
    
    public void setAnswerTime(Timestamp answerTime) {
        this.answerTime = answerTime;
    }
    
    public Question getQuestion() {
        return question;
    }
    
    public void setQuestion(Question question) {
        this.question = question;
        if (question != null) {
            this.questionId = question.getQuestionId();
            this.correctAnswer = question.getCorrectAnswer();
            // 重新计算是否正确
            this.isCorrect = (userAnswer != null && userAnswer.equals(correctAnswer));
        }
    }
    
    // 业务方法
    
    /**
     * 获取用户答案的文本内容
     * @return 用户答案文本
     */
    public String getUserAnswerText() {
        if (userAnswer == null || question == null) {
            return "未作答";
        }
        return userAnswer.getCode() + ". " + question.getOptionByAnswer(userAnswer);
    }
    
    /**
     * 获取正确答案的文本内容
     * @return 正确答案文本
     */
    public String getCorrectAnswerText() {
        if (correctAnswer == null || question == null) {
            return "未知";
        }
        return correctAnswer.getCode() + ". " + question.getOptionByAnswer(correctAnswer);
    }
    
    /**
     * 获取答题结果描述
     * @return 答题结果（正确/错误/未作答）
     */
    public String getResultDescription() {
        if (userAnswer == null) {
            return "未作答";
        }
        return isCorrect ? "正确" : "错误";
    }
    
    /**
     * 检查是否已作答
     * @return true如果已作答，否则false
     */
    public boolean isAnswered() {
        return userAnswer != null;
    }
    
    /**
     * 获取得分（正确得1分，错误或未答得0分）
     * @return 得分
     */
    public int getScore() {
        return (isCorrect != null && isCorrect) ? 1 : 0;
    }
    
    /**
     * 重新计算答题正确性
     */
    public void recalculateCorrectness() {
        this.isCorrect = (userAnswer != null && userAnswer.equals(correctAnswer));
    }
    
    /**
     * 获取答题状态图标
     * @return 状态图标字符串
     */
    public String getStatusIcon() {
        if (userAnswer == null) {
            return "○"; // 未作答
        }
        return isCorrect ? "✓" : "✗"; // 正确或错误
    }
    
    @Override
    public String toString() {
        return "ExamDetail{" +
                "detailId=" + detailId +
                ", recordId=" + recordId +
                ", questionId=" + questionId +
                ", userAnswer=" + userAnswer +
                ", correctAnswer=" + correctAnswer +
                ", isCorrect=" + isCorrect +
                ", answerTime=" + answerTime +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ExamDetail that = (ExamDetail) obj;
        return detailId != null && detailId.equals(that.detailId);
    }
    
    @Override
    public int hashCode() {
        return detailId != null ? detailId.hashCode() : 0;
    }
}
