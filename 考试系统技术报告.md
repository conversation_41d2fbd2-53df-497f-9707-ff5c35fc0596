# 网上考试系统技术报告

## 1 系统需求分析

### 1.1 系统需求

本系统是一个基于C/S架构的网上考试系统，主要满足以下需求：

1. **多用户支持**：支持学生和管理员两种用户角色，实现多用户并发访问
2. **用户管理**：提供用户注册、登录、权限管理功能
3. **题库管理**：管理员可以对试题进行增、删、改操作
4. **在线考试**：学生可以参加在线考试，系统随机抽取题目
5. **成绩管理**：自动计算和保存考试成绩，支持成绩查询
6. **网络通信**：基于Socket实现客户端与服务器的通信
7. **数据持久化**：使用MySQL数据库存储所有数据

### 1.2 系统功能

#### 1.2.1 用户功能模块
- **用户注册**：新用户可以注册账户，选择用户类型（学生/管理员）
- **用户登录**：已注册用户通过用户名和密码登录系统
- **权限控制**：根据用户类型提供不同的功能权限

#### 1.2.2 题库管理模块（管理员）
- **题目添加**：管理员可以添加新的选择题
- **题目编辑**：修改已有题目的内容、选项和答案
- **题目删除**：删除不需要的题目（逻辑删除）
- **题目查询**：查看所有题目列表和详细信息

#### 1.2.3 考试模块（学生）
- **考试设置**：学生可以设置考试题目数量和考试时长
- **随机抽题**：系统从题库中随机选择指定数量的题目
- **在线答题**：提供友好的答题界面，支持题目导航
- **时间控制**：实时显示剩余时间，时间到自动提交
- **答案提交**：学生完成答题后提交答案

#### 1.2.4 成绩管理模块
- **自动评分**：系统自动计算考试成绩和正确率
- **成绩保存**：将考试记录和详细答题情况保存到数据库
- **成绩查询**：学生查看个人成绩，管理员查看所有成绩
- **统计分析**：提供成绩统计和分析功能

### 1.3 系统的主界面

系统采用多界面设计，主要包括：

1. **登录界面**：用户身份验证入口
2. **注册界面**：新用户注册功能
3. **管理员主界面**：题目管理和成绩查看
4. **学生主界面**：考试和成绩查看
5. **考试界面**：在线答题界面

*[界面截图位置预留]*

## 2 系统设计

### 2.1 总体设计思路

本系统采用经典的C/S（Client/Server）架构模式，结合MVC设计模式，具有以下特点：

1. **架构设计**：
   - 服务器端：负责业务逻辑处理、数据管理和客户端请求响应
   - 客户端：负责用户界面展示和用户交互
   - 数据库：负责数据持久化存储

2. **设计原则**：
   - **MVC模式**：Model（数据模型）、View（视图界面）、Controller（控制器）分离
   - **开闭原则**：对扩展开放，对修改关闭，便于系统功能扩展
   - **单一职责**：每个类和模块都有明确的职责
   - **松耦合**：模块间依赖关系最小化

3. **技术选型**：
   - **开发语言**：Java 8
   - **用户界面**：Swing GUI框架
   - **数据库**：MySQL 8.0
   - **网络通信**：Socket + JSON
   - **构建工具**：Maven
   - **设计模式**：MVC、单例、工厂等

### 2.2 数据库设计

#### 2.2.1 数据库

数据库名称：`exam_system`
字符集：`utf8mb4`
排序规则：`utf8mb4_unicode_ci`

#### 2.2.2 数据库表设计

**1. 用户表（users）**
```sql
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户编号',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    user_type ENUM('STUDENT', 'ADMIN') NOT NULL DEFAULT 'STUDENT' COMMENT '用户类型',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态'
);
```

**2. 试题表（questions）**
```sql
CREATE TABLE questions (
    question_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '题目编号',
    question_content TEXT NOT NULL COMMENT '题目内容',
    option_a VARCHAR(500) NOT NULL COMMENT '选项A',
    option_b VARCHAR(500) NOT NULL COMMENT '选项B',
    option_c VARCHAR(500) NOT NULL COMMENT '选项C',
    option_d VARCHAR(500) NOT NULL COMMENT '选项D',
    correct_answer ENUM('A', 'B', 'C', 'D') NOT NULL COMMENT '正确答案',
    difficulty ENUM('EASY', 'MEDIUM', 'HARD') DEFAULT 'MEDIUM' COMMENT '难度等级',
    subject VARCHAR(100) DEFAULT '计算机基础' COMMENT '科目',
    created_by INT COMMENT '创建者ID',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态'
);
```

**3. 考试记录表（exam_records）**
```sql
CREATE TABLE exam_records (
    record_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '记录编号',
    user_id INT NOT NULL COMMENT '用户编号',
    user_name VARCHAR(50) NOT NULL COMMENT '用户姓名',
    exam_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '考试时间',
    total_questions INT NOT NULL DEFAULT 0 COMMENT '总题数',
    correct_answers INT NOT NULL DEFAULT 0 COMMENT '正确答案数',
    score DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '考试成绩',
    time_used INT NOT NULL DEFAULT 0 COMMENT '用时（秒）',
    exam_duration INT NOT NULL DEFAULT 1800 COMMENT '考试时长（秒）',
    status ENUM('COMPLETED', 'TIMEOUT', 'INTERRUPTED') DEFAULT 'COMPLETED' COMMENT '考试状态'
);
```

**4. 考试详情表（exam_details）**
```sql
CREATE TABLE exam_details (
    detail_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '详情编号',
    record_id INT NOT NULL COMMENT '考试记录编号',
    question_id INT NOT NULL COMMENT '题目编号',
    user_answer ENUM('A', 'B', 'C', 'D') COMMENT '用户答案',
    correct_answer ENUM('A', 'B', 'C', 'D') NOT NULL COMMENT '正确答案',
    is_correct BOOLEAN DEFAULT FALSE COMMENT '是否正确',
    answer_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '答题时间'
);
```

### 2.3 类的设计

#### 2.3.1 视图中的类

**客户端UI类（com.exam.client.ui包）**

1. **LoginFrame**：登录界面
   - 功能：用户登录、跳转注册
   - 主要方法：performLogin(), showRegisterDialog()

2. **RegisterFrame**：注册界面
   - 功能：用户注册、输入验证
   - 主要方法：performRegister(), validateInput()

3. **AdminFrame**：管理员主界面
   - 功能：题目管理、成绩查看
   - 主要方法：loadQuestions(), loadScores(), showAddQuestionDialog()

4. **StudentFrame**：学生主界面
   - 功能：开始考试、查看成绩
   - 主要方法：startExam(), loadScores()

5. **ExamFrame**：考试界面
   - 功能：在线答题、时间控制、答案提交
   - 主要方法：loadQuestion(), saveCurrentAnswer(), submitExam()

#### 2.3.2 控制器中的类

**控制器类（com.exam.controller包）**

1. **UserController**：用户控制器
   - 功能：用户登录、注册、权限验证
   - 主要方法：login(), register(), isAdmin(), hasPermission()

2. **QuestionController**：题目控制器
   - 功能：题目管理、验证
   - 主要方法：getAllQuestions(), addQuestion(), updateQuestion(), deleteQuestion()

3. **ExamController**：考试控制器
   - 功能：考试管理、成绩计算
   - 主要方法：startExam(), submitExam(), calculateExamScore(), getScoreList()

#### 2.3.3 模型中的类

**实体模型类（com.exam.model包）**

1. **User**：用户实体
   - 属性：userId, username, password, realName, userType, email, phone
   - 方法：isAdmin(), isStudent(), isValid()

2. **Question**：题目实体
   - 属性：questionId, questionContent, optionA-D, correctAnswer, difficulty, subject
   - 方法：getOptionByAnswer(), isCorrect(), isValid()

3. **ExamRecord**：考试记录实体
   - 属性：recordId, userId, userName, examTime, totalQuestions, correctAnswers, score
   - 方法：calculateScore(), isPassed(), getAccuracy()

4. **ExamDetail**：考试详情实体
   - 属性：detailId, recordId, questionId, userAnswer, correctAnswer, isCorrect
   - 方法：getUserAnswerText(), getResultDescription()

5. **Message**：网络消息实体
   - 属性：messageId, type, status, message, data, params
   - 方法：addParam(), isSuccess(), createSuccessResponse()

### 2.4 系统逻辑结构设计

#### 2.4.1 系统总体逻辑结构

```mermaid
graph TB
    subgraph "客户端层"
        A[登录界面 LoginFrame]
        B[注册界面 RegisterFrame]
        C[管理员界面 AdminFrame]
        D[学生界面 StudentFrame]
        E[考试界面 ExamFrame]
    end

    subgraph "控制器层"
        F[用户控制器 UserController]
        G[题目控制器 QuestionController]
        H[考试控制器 ExamController]
    end

    subgraph "网络通信层"
        I[客户端 ExamClient]
        J[网络工具 NetworkUtil]
        K[消息实体 Message]
    end

    subgraph "服务器层"
        L[考试服务器 ExamServer]
        M[客户端处理器 ClientHandler]
        N[数据库管理器 DatabaseManager]
    end

    subgraph "数据层"
        O[MySQL数据库]
        P[数据库工具 DatabaseUtil]
        Q[文件工具 FileUtil]
    end

    A --> F
    B --> F
    C --> G
    C --> H
    D --> H
    E --> H

    F --> I
    G --> I
    H --> I

    I --> J
    J --> K

    K --> L
    L --> M
    M --> N

    N --> P
    P --> O
    N --> Q
```

#### 2.4.2 系统详细逻辑结构

**类依赖和继承关系UML图**

```mermaid
classDiagram
    class User {
        -Integer userId
        -String username
        -String password
        -String realName
        -UserType userType
        +isAdmin() boolean
        +isStudent() boolean
        +isValid() boolean
    }

    class Question {
        -Integer questionId
        -String questionContent
        -String optionA, optionB, optionC, optionD
        -Answer correctAnswer
        -Difficulty difficulty
        +getOptionByAnswer(Answer) String
        +isCorrect(Answer) boolean
        +isValid() boolean
    }

    class ExamRecord {
        -Integer recordId
        -Integer userId
        -String userName
        -Integer totalQuestions
        -Integer correctAnswers
        -BigDecimal score
        +calculateScore() void
        +isPassed(double) boolean
        +getAccuracy() double
    }

    class ExamDetail {
        -Integer detailId
        -Integer recordId
        -Integer questionId
        -Answer userAnswer
        -Answer correctAnswer
        -Boolean isCorrect
        +getUserAnswerText() String
        +getResultDescription() String
    }

    class Message {
        -String messageId
        -MessageType type
        -MessageStatus status
        -Object data
        +addParam(String, Object) Message
        +isSuccess() boolean
        +createSuccessResponse(MessageType, Object) Message
    }

    class ExamClient {
        -Socket socket
        -User currentUser
        -String sessionId
        +connectToServer() boolean
        +login(String, String) Message
        +register(User) Message
        +sendMessage(Message) Message
    }

    class ExamServer {
        -ServerSocket serverSocket
        -DatabaseManager dbManager
        -ExecutorService threadPool
        +start(int) void
        +handleNewClient(Socket) void
    }

    class ClientHandler {
        -Socket clientSocket
        -DatabaseManager dbManager
        -User currentUser
        +handleRequest(Message) Message
        +handleLogin(Message) Message
        +handleExam(Message) Message
    }

    class DatabaseManager {
        +authenticateUser(String, String) User
        +registerUser(User) boolean
        +getAllQuestions() List~Question~
        +addQuestion(Question) boolean
        +saveExamRecord(ExamRecord) Integer
    }

    class UserController {
        -ExamClient client
        +login(String, String) Message
        +register(User) Message
        +isAdmin() boolean
        +hasPermission(UserPermission) boolean
    }

    class QuestionController {
        -ExamClient client
        +getAllQuestions() Message
        +addQuestion(Question) Message
        +updateQuestion(Question) Message
        +deleteQuestion(Integer) Message
    }

    class ExamController {
        -ExamClient client
        +startExam(int, int) Message
        +submitExam(ExamRecord) Message
        +calculateExamScore(List~ExamDetail~, int, int) ExamRecord
    }

    ExamRecord ||--o{ ExamDetail : contains
    ExamDetail }o--|| Question : references
    ExamRecord }o--|| User : belongs to

    ExamClient --> UserController : uses
    ExamClient --> QuestionController : uses
    ExamClient --> ExamController : uses

    ExamServer --> ClientHandler : creates
    ClientHandler --> DatabaseManager : uses

    UserController --> Message : creates
    QuestionController --> Message : creates
    ExamController --> Message : creates
```

## 3 系统实现与功能展示

### 3.1 主界面

系统采用多窗口设计，根据用户角色显示不同的主界面：

- **登录界面**：系统入口，提供用户身份验证
- **管理员主界面**：提供题目管理和成绩查看功能
- **学生主界面**：提供考试和个人成绩查看功能

*[主界面截图位置预留 - 显示登录界面、管理员界面、学生界面]*

### 3.2 用户注册登录模块

#### 3.2.1 用户注册登录模块代码实现

**1. 用户实体类（User.java）**
```java
/**
 * 用户实体类 - 实现MVC模式中的Model层
 */
public class User implements Serializable {
    // 用户类型枚举
    public enum UserType {
        STUDENT("STUDENT", "学生"),
        ADMIN("ADMIN", "管理员");
    }

    // 用户属性
    private Integer userId;          // 用户编号（主键）
    private String username;         // 用户名
    private String password;         // 密码
    private String realName;         // 真实姓名
    private UserType userType;       // 用户类型
    private String email;            // 邮箱
    private String phone;            // 电话

    // 业务方法
    public boolean isAdmin() {
        return UserType.ADMIN.equals(this.userType);
    }

    public boolean isStudent() {
        return UserType.STUDENT.equals(this.userType);
    }

    public boolean isValid() {
        return username != null && !username.trim().isEmpty() &&
               password != null && !password.trim().isEmpty() &&
               realName != null && !realName.trim().isEmpty() &&
               userType != null;
    }
}
```

**2. 用户控制器（UserController.java）**
```java
/**
 * 用户控制器 - 实现MVC模式中的Controller层
 */
public class UserController {
    private ExamClient client;

    /**
     * 用户登录
     */
    public Message login(String username, String password) {
        // 验证输入参数
        if (username == null || username.trim().isEmpty()) {
            return createErrorMessage("用户名不能为空");
        }

        if (password == null || password.trim().isEmpty()) {
            return createErrorMessage("密码不能为空");
        }

        // 调用客户端登录方法
        return client.login(username.trim(), password);
    }

    /**
     * 用户注册
     */
    public Message register(User user) {
        // 验证用户信息
        String validationError = validateUser(user);
        if (validationError != null) {
            return createErrorMessage(validationError);
        }

        // 调用客户端注册方法
        return client.register(user);
    }

    /**
     * 验证用户信息
     */
    private String validateUser(User user) {
        if (user == null) {
            return "用户信息不能为空";
        }

        // 验证用户名
        if (user.getUsername() == null || user.getUsername().trim().isEmpty()) {
            return "用户名不能为空";
        }

        if (user.getUsername().length() < 3) {
            return "用户名至少需要3个字符";
        }

        // 检查用户名格式（只允许字母、数字、下划线）
        if (!user.getUsername().matches("^[a-zA-Z0-9_]+$")) {
            return "用户名只能包含字母、数字和下划线";
        }

        // 验证密码
        if (user.getPassword() == null || user.getPassword().trim().isEmpty()) {
            return "密码不能为空";
        }

        if (user.getPassword().length() < 6) {
            return "密码至少需要6个字符";
        }

        return null; // 验证通过
    }
}
```

**3. 登录界面（LoginFrame.java）**
```java
/**
 * 登录界面 - 实现MVC模式中的View层
 */
public class LoginFrame extends JFrame {
    private ExamClient client;
    private JTextField usernameField;
    private JPasswordField passwordField;
    private JButton loginButton;

    /**
     * 执行登录操作
     */
    private void performLogin() {
        String username = usernameField.getText().trim();
        String password = new String(passwordField.getPassword());

        // 验证输入
        if (username.isEmpty()) {
            showStatus("请输入用户名", Color.RED);
            return;
        }

        if (password.isEmpty()) {
            showStatus("请输入密码", Color.RED);
            return;
        }

        // 在后台线程中执行登录
        SwingWorker<Message, Void> loginWorker = new SwingWorker<Message, Void>() {
            @Override
            protected Message doInBackground() throws Exception {
                return client.login(username, password);
            }

            @Override
            protected void done() {
                try {
                    Message response = get();
                    handleLoginResponse(response);
                } catch (Exception e) {
                    showStatus("登录失败: " + e.getMessage(), Color.RED);
                }
            }
        };

        loginWorker.execute();
    }

    /**
     * 处理登录响应
     */
    private void handleLoginResponse(Message response) {
        if (response.isSuccess()) {
            showStatus("登录成功", Color.GREEN);

            // 根据用户类型打开相应界面
            User user = client.getCurrentUser();
            if (user.isAdmin()) {
                // 打开管理员界面
                AdminFrame adminFrame = new AdminFrame(client);
                adminFrame.setVisible(true);
                dispose();
            } else {
                // 打开学生界面
                StudentFrame studentFrame = new StudentFrame(client);
                studentFrame.setVisible(true);
                dispose();
            }
        } else {
            showStatus("登录失败: " + response.getMessage(), Color.RED);
        }
    }
}
```

#### 3.2.2 用户注册登录模块运行结果

**(1) 登录界面展示**
- 界面包含用户名输入框、密码输入框、登录按钮、注册按钮
- 支持回车键快速登录
- 显示服务器连接状态和操作提示信息

*[登录界面截图位置预留]*

**(2) 注册功能展示**
- 提供完整的用户注册表单
- 包含用户名、密码、确认密码、真实姓名、邮箱、电话、用户类型等字段
- 实时验证输入格式和完整性
- 支持学生和管理员两种用户类型注册

*[注册界面截图位置预留]*

**(3) 登录验证功能**
- 用户名和密码验证
- 错误提示信息显示
- 登录成功后根据用户类型跳转到相应界面

*[登录验证截图位置预留]*

### 3.3 题目管理模块

#### 3.3.1 题目管理模块代码实现

**1. 题目实体类（Question.java）**
```java
/**
 * 试题实体类 - 实现MVC模式中的Model层
 */
public class Question implements Serializable {
    // 答案选项枚举
    public enum Answer {
        A("A", "选项A"), B("B", "选项B"),
        C("C", "选项C"), D("D", "选项D");

        public static Answer fromCode(String code) {
            for (Answer answer : values()) {
                if (answer.code.equals(code)) {
                    return answer;
                }
            }
            return null;
        }
    }

    // 难度等级枚举
    public enum Difficulty {
        EASY("EASY", "简单"),
        MEDIUM("MEDIUM", "中等"),
        HARD("HARD", "困难");
    }

    // 试题属性
    private Integer questionId;        // 题目编号（主键）
    private String questionContent;    // 题目内容
    private String optionA, optionB, optionC, optionD;  // 四个选项
    private Answer correctAnswer;      // 正确答案
    private Difficulty difficulty;     // 难度等级
    private String subject;            // 科目

    /**
     * 根据选项代码获取选项内容
     */
    public String getOptionByAnswer(Answer answer) {
        if (answer == null) return "";
        switch (answer) {
            case A: return optionA;
            case B: return optionB;
            case C: return optionC;
            case D: return optionD;
            default: return "";
        }
    }

    /**
     * 检查答案是否正确
     */
    public boolean isCorrect(Answer userAnswer) {
        return correctAnswer != null && correctAnswer.equals(userAnswer);
    }

    /**
     * 验证题目信息是否完整
     */
    public boolean isValid() {
        return questionContent != null && !questionContent.trim().isEmpty() &&
               optionA != null && !optionA.trim().isEmpty() &&
               optionB != null && !optionB.trim().isEmpty() &&
               optionC != null && !optionC.trim().isEmpty() &&
               optionD != null && !optionD.trim().isEmpty() &&
               correctAnswer != null;
    }
}
```

**2. 题目控制器（QuestionController.java）**
```java
/**
 * 题目控制器 - 实现MVC模式中的Controller层
 */
public class QuestionController {
    private ExamClient client;

    /**
     * 获取所有题目列表
     */
    public Message getAllQuestions() {
        try {
            Message request = new Message(Message.MessageType.QUESTION_LIST_REQUEST);
            return client.sendMessage(request);
        } catch (IOException e) {
            return createErrorMessage("获取题目列表失败: " + e.getMessage());
        }
    }

    /**
     * 添加新题目
     */
    public Message addQuestion(Question question) {
        // 验证题目信息
        String validationError = validateQuestion(question);
        if (validationError != null) {
            return createErrorMessage(validationError);
        }

        try {
            Message request = new Message(Message.MessageType.QUESTION_ADD_REQUEST, question);
            return client.sendMessage(request);
        } catch (IOException e) {
            return createErrorMessage("添加题目失败: " + e.getMessage());
        }
    }

    /**
     * 更新题目
     */
    public Message updateQuestion(Question question) {
        String validationError = validateQuestion(question);
        if (validationError != null) {
            return createErrorMessage(validationError);
        }

        if (question.getQuestionId() == null) {
            return createErrorMessage("题目ID不能为空");
        }

        try {
            Message request = new Message(Message.MessageType.QUESTION_UPDATE_REQUEST, question);
            return client.sendMessage(request);
        } catch (IOException e) {
            return createErrorMessage("更新题目失败: " + e.getMessage());
        }
    }

    /**
     * 删除题目
     */
    public Message deleteQuestion(Integer questionId) {
        if (questionId == null) {
            return createErrorMessage("题目ID不能为空");
        }

        try {
            Message request = new Message(Message.MessageType.QUESTION_DELETE_REQUEST);
            request.addParam("questionId", questionId);
            return client.sendMessage(request);
        } catch (IOException e) {
            return createErrorMessage("删除题目失败: " + e.getMessage());
        }
    }
}
```

**3. 管理员界面题目管理部分（AdminFrame.java）**
```java
/**
 * 管理员主界面 - 实现MVC模式中的View层
 */
public class AdminFrame extends JFrame {
    private QuestionController questionController;
    private JTable questionTable;
    private DefaultTableModel questionTableModel;

    /**
     * 创建题目管理面板
     */
    private JPanel createQuestionManagementPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // 工具栏
        JPanel toolBar = new JPanel(new FlowLayout(FlowLayout.LEFT));

        JButton addButton = new JButton("添加题目");
        JButton editButton = new JButton("编辑题目");
        JButton deleteButton = new JButton("删除题目");
        JButton refreshButton = new JButton("刷新");

        addButton.addActionListener(e -> showAddQuestionDialog());
        editButton.addActionListener(e -> showEditQuestionDialog());
        deleteButton.addActionListener(e -> deleteSelectedQuestion());
        refreshButton.addActionListener(e -> loadQuestions());

        toolBar.add(addButton);
        toolBar.add(editButton);
        toolBar.add(deleteButton);
        toolBar.add(refreshButton);

        panel.add(toolBar, BorderLayout.NORTH);

        // 题目表格
        JScrollPane scrollPane = new JScrollPane(questionTable);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * 加载题目列表
     */
    private void loadQuestions() {
        SwingWorker<Message, Void> worker = new SwingWorker<Message, Void>() {
            @Override
            protected Message doInBackground() throws Exception {
                return questionController.getAllQuestions();
            }

            @Override
            protected void done() {
                try {
                    Message response = get();
                    if (response.isSuccess()) {
                        List<Question> questions = (List<Question>) response.getData();
                        updateQuestionTable(questions);
                        setStatus("题目列表加载完成，共" + questions.size() + "道题目");
                    } else {
                        setStatus("加载题目失败: " + response.getMessage());
                    }
                } catch (Exception e) {
                    setStatus("加载题目异常: " + e.getMessage());
                }
            }
        };
        worker.execute();
    }

    /**
     * 删除选中的题目
     */
    private void deleteSelectedQuestion() {
        int selectedRow = questionTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "请选择要删除的题目", "提示",
                JOptionPane.WARNING_MESSAGE);
            return;
        }

        int confirm = JOptionPane.showConfirmDialog(this,
            "确定要删除选中的题目吗？", "确认删除",
            JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);

        if (confirm == JOptionPane.YES_OPTION) {
            Integer questionId = (Integer) questionTableModel.getValueAt(selectedRow, 0);

            SwingWorker<Message, Void> worker = new SwingWorker<Message, Void>() {
                @Override
                protected Message doInBackground() throws Exception {
                    return questionController.deleteQuestion(questionId);
                }

                @Override
                protected void done() {
                    try {
                        Message response = get();
                        if (response.isSuccess()) {
                            setStatus("题目删除成功");
                            loadQuestions(); // 重新加载题目列表
                        } else {
                            setStatus("删除题目失败: " + response.getMessage());
                        }
                    } catch (Exception e) {
                        setStatus("删除题目异常: " + e.getMessage());
                    }
                }
            };
            worker.execute();
        }
    }
}
```

#### 3.3.2 题目管理模块运行结果

**(1) 题目列表展示**
- 以表格形式显示所有题目信息
- 包含题目ID、题目内容、正确答案、难度、科目、创建时间等字段
- 支持题目的增删改查操作

*[题目列表界面截图位置预留]*

**(2) 添加题目功能**
- 提供完整的题目添加表单
- 包含题目内容、四个选项、正确答案、难度等级、科目等字段
- 实时验证输入的完整性和正确性

*[添加题目界面截图位置预留]*

**(3) 编辑和删除题目功能**
- 支持选中题目进行编辑
- 提供删除确认对话框
- 操作完成后自动刷新题目列表

*[编辑删除题目截图位置预留]*

### 3.4 在线考试模块

#### 3.4.1 在线考试模块代码实现

**1. 考试记录实体类（ExamRecord.java）**
```java
/**
 * 考试记录实体类 - 实现MVC模式中的Model层
 */
public class ExamRecord implements Serializable {
    // 考试状态枚举
    public enum ExamStatus {
        COMPLETED("COMPLETED", "已完成"),
        TIMEOUT("TIMEOUT", "超时"),
        INTERRUPTED("INTERRUPTED", "中断");
    }

    // 考试记录属性
    private Integer recordId;          // 记录编号（主键）
    private Integer userId;            // 用户编号
    private String userName;           // 用户姓名
    private Timestamp examTime;        // 考试时间
    private Integer totalQuestions;    // 总题数
    private Integer correctAnswers;    // 正确答案数
    private BigDecimal score;          // 考试成绩
    private Integer timeUsed;          // 用时（秒）
    private Integer examDuration;      // 考试时长（秒）
    private ExamStatus status;         // 考试状态

    /**
     * 计算考试成绩
     * 成绩 = (正确答案数 / 总题数) * 100
     */
    public void calculateScore() {
        if (totalQuestions > 0) {
            double scoreValue = (double) correctAnswers / totalQuestions * 100;
            this.score = BigDecimal.valueOf(scoreValue).setScale(2, BigDecimal.ROUND_HALF_UP);
        } else {
            this.score = BigDecimal.ZERO;
        }
    }

    /**
     * 检查是否通过考试
     */
    public boolean isPassed(double passScore) {
        return score.doubleValue() >= passScore;
    }

    /**
     * 获取成绩等级
     */
    public String getGradeLevel() {
        double scoreValue = score.doubleValue();
        if (scoreValue >= 90) return "优秀";
        else if (scoreValue >= 80) return "良好";
        else if (scoreValue >= 70) return "中等";
        else if (scoreValue >= 60) return "及格";
        else return "不及格";
    }
}
```

**2. 考试控制器（ExamController.java）**
```java
/**
 * 考试控制器 - 实现MVC模式中的Controller层
 */
public class ExamController {
    private ExamClient client;

    /**
     * 开始考试
     */
    public Message startExam(int questionCount, int examDuration) {
        // 验证参数
        if (questionCount <= 0) {
            return createErrorMessage("题目数量必须大于0");
        }

        if (examDuration <= 0) {
            return createErrorMessage("考试时长必须大于0");
        }

        try {
            Message request = new Message(Message.MessageType.EXAM_START_REQUEST);
            request.addParam("questionCount", questionCount);
            request.addParam("examDuration", examDuration);

            return client.sendMessage(request);
        } catch (IOException e) {
            return createErrorMessage("开始考试失败: " + e.getMessage());
        }
    }

    /**
     * 提交考试
     */
    public Message submitExam(ExamRecord examRecord) {
        String validationError = validateExamRecord(examRecord);
        if (validationError != null) {
            return createErrorMessage(validationError);
        }

        try {
            Message request = new Message(Message.MessageType.EXAM_SUBMIT_REQUEST, examRecord);
            return client.sendMessage(request);
        } catch (IOException e) {
            return createErrorMessage("提交考试失败: " + e.getMessage());
        }
    }

    /**
     * 计算考试成绩
     */
    public ExamRecord calculateExamScore(List<ExamDetail> examDetails, int timeUsed, int examDuration) {
        User currentUser = client.getCurrentUser();
        if (currentUser == null || examDetails == null) {
            return null;
        }

        ExamRecord examRecord = new ExamRecord();
        examRecord.setUserId(currentUser.getUserId());
        examRecord.setUserName(currentUser.getRealName());
        examRecord.setTotalQuestions(examDetails.size());
        examRecord.setTimeUsed(timeUsed);
        examRecord.setExamDuration(examDuration);
        examRecord.setExamDetails(examDetails);

        // 计算正确答案数
        int correctCount = 0;
        for (ExamDetail detail : examDetails) {
            if (detail.isCorrect()) {
                correctCount++;
            }
        }
        examRecord.setCorrectAnswers(correctCount);

        // 计算成绩
        examRecord.calculateScore();

        // 设置考试状态
        if (timeUsed >= examDuration) {
            examRecord.setStatus(ExamRecord.ExamStatus.TIMEOUT);
        } else {
            examRecord.setStatus(ExamRecord.ExamStatus.COMPLETED);
        }

        return examRecord;
    }
}
```

**3. 考试界面（ExamFrame.java）**
```java
/**
 * 考试界面 - 实现MVC模式中的View层
 */
public class ExamFrame extends JFrame {
    private ExamController examController;
    private List<Question> questions;
    private List<Question.Answer> userAnswers;
    private int currentQuestionIndex;
    private int examDuration;
    private int timeRemaining;
    private Timer examTimer;

    /**
     * 启动考试计时器
     */
    private void startExamTimer() {
        examTimer = new Timer(1000, new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                timeRemaining--;
                updateTimeDisplay();

                if (timeRemaining <= 0) {
                    examTimer.stop();
                    JOptionPane.showMessageDialog(ExamFrame.this,
                        "考试时间到！系统将自动提交答案。",
                        "时间到", JOptionPane.WARNING_MESSAGE);
                    submitExam();
                }
            }
        });
        examTimer.start();
    }

    /**
     * 加载指定题目
     */
    private void loadQuestion(int index) {
        if (index < 0 || index >= questions.size()) {
            return;
        }

        currentQuestionIndex = index;
        Question question = questions.get(index);

        // 显示题目内容
        questionLabel.setText("<html><body style='width: 500px'>" +
            "第" + (index + 1) + "题：" + question.getQuestionContent() +
            "</body></html>");

        // 显示选项
        optionA.setText("A. " + question.getOptionA());
        optionB.setText("B. " + question.getOptionB());
        optionC.setText("C. " + question.getOptionC());
        optionD.setText("D. " + question.getOptionD());

        // 恢复用户答案
        Question.Answer userAnswer = userAnswers.get(index);
        optionGroup.clearSelection();

        if (userAnswer != null) {
            switch (userAnswer) {
                case A: optionA.setSelected(true); break;
                case B: optionB.setSelected(true); break;
                case C: optionC.setSelected(true); break;
                case D: optionD.setSelected(true); break;
            }
        }

        updateProgressDisplay();
    }

    /**
     * 提交考试
     */
    private void submitExam() {
        saveCurrentAnswer();

        // 检查未答题目
        int unansweredCount = 0;
        for (Question.Answer answer : userAnswers) {
            if (answer == null) {
                unansweredCount++;
            }
        }

        if (unansweredCount > 0) {
            int option = JOptionPane.showConfirmDialog(this,
                "还有" + unansweredCount + "道题目未作答，确定要提交吗？",
                "确认提交", JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);

            if (option != JOptionPane.YES_OPTION) {
                return;
            }
        }

        // 停止计时器
        if (examTimer != null) {
            examTimer.stop();
        }

        // 计算考试结果
        int timeUsed = examDuration - timeRemaining;
        List<ExamDetail> examDetails = new ArrayList<>();

        for (int i = 0; i < questions.size(); i++) {
            ExamDetail detail = examController.createExamDetail(
                null, questions.get(i), userAnswers.get(i));
            examDetails.add(detail);
        }

        ExamRecord examRecord = examController.calculateExamScore(
            examDetails, timeUsed, examDuration);

        // 提交到服务器
        SwingWorker<Message, Void> worker = new SwingWorker<Message, Void>() {
            @Override
            protected Message doInBackground() throws Exception {
                return examController.submitExam(examRecord);
            }

            @Override
            protected void done() {
                try {
                    Message response = get();
                    if (response.isSuccess()) {
                        showExamResult(examRecord);
                    } else {
                        JOptionPane.showMessageDialog(ExamFrame.this,
                            "提交考试失败: " + response.getMessage(),
                            "错误", JOptionPane.ERROR_MESSAGE);
                    }
                } catch (Exception e) {
                    JOptionPane.showMessageDialog(ExamFrame.this,
                        "提交考试异常: " + e.getMessage(),
                        "错误", JOptionPane.ERROR_MESSAGE);
                }
            }
        };
        worker.execute();
    }
}
```

#### 3.4.2 在线考试模块运行结果

**(1) 考试设置界面**
- 学生可以设置考试题目数量（5-50题）
- 设置考试时长（5-120分钟）
- 显示考试说明和注意事项

*[考试设置界面截图位置预留]*

**(2) 在线答题界面**
- 左侧显示题目导航列表，标记已答题目
- 中央显示当前题目内容和四个选项
- 顶部显示剩余时间和答题进度
- 底部提供上一题、下一题、提交考试按钮

*[在线答题界面截图位置预留]*

**(3) 考试结果展示**
- 显示考试完成信息
- 包含总题数、正确答案数、考试成绩、用时、成绩等级
- 自动保存考试记录到数据库

*[考试结果界面截图位置预留]*

### 3.5 成绩管理模块

#### 3.5.1 成绩管理模块代码实现

**服务器端成绩管理（DatabaseManager.java）**
```java
/**
 * 数据库管理器 - 成绩管理相关方法
 */
public class DatabaseManager {

    /**
     * 保存考试记录
     */
    public Integer saveExamRecord(ExamRecord examRecord) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DatabaseUtil.getConnection();
            String sql = "INSERT INTO exam_records (user_id, user_name, total_questions, " +
                        "correct_answers, score, time_used, exam_duration, status) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            pstmt.setInt(1, examRecord.getUserId());
            pstmt.setString(2, examRecord.getUserName());
            pstmt.setInt(3, examRecord.getTotalQuestions());
            pstmt.setInt(4, examRecord.getCorrectAnswers());
            pstmt.setBigDecimal(5, examRecord.getScore());
            pstmt.setInt(6, examRecord.getTimeUsed());
            pstmt.setInt(7, examRecord.getExamDuration());
            pstmt.setString(8, examRecord.getStatus().getCode());

            int result = pstmt.executeUpdate();

            if (result > 0) {
                rs = pstmt.getGeneratedKeys();
                if (rs.next()) {
                    int recordId = rs.getInt(1);
                    examRecord.setRecordId(recordId);

                    // 保存考试详情
                    if (saveExamDetails(conn, examRecord.getExamDetails())) {
                        DatabaseUtil.commit(conn);
                        return recordId;
                    }
                }
            }

            DatabaseUtil.rollback(conn);
            return null;

        } catch (SQLException e) {
            System.err.println("保存考试记录失败: " + e.getMessage());
            DatabaseUtil.rollback(conn);
            return null;
        } finally {
            DatabaseUtil.closeAll(conn, pstmt, rs);
        }
    }

    /**
     * 获取用户考试记录
     */
    public List<ExamRecord> getUserExamRecords(int userId) {
        List<ExamRecord> records = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DatabaseUtil.getConnection();
            String sql = "SELECT * FROM exam_records WHERE user_id = ? ORDER BY exam_time DESC";
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, userId);

            rs = pstmt.executeQuery();

            while (rs.next()) {
                records.add(mapResultSetToExamRecord(rs));
            }

        } catch (SQLException e) {
            System.err.println("获取考试记录失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeAll(conn, pstmt, rs);
        }

        return records;
    }

    /**
     * 获取所有考试记录（管理员用）
     */
    public List<ExamRecord> getAllExamRecords() {
        List<ExamRecord> records = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DatabaseUtil.getConnection();
            String sql = "SELECT * FROM exam_records ORDER BY exam_time DESC";
            pstmt = conn.prepareStatement(sql);

            rs = pstmt.executeQuery();

            while (rs.next()) {
                records.add(mapResultSetToExamRecord(rs));
            }

        } catch (SQLException e) {
            System.err.println("获取所有考试记录失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeAll(conn, pstmt, rs);
        }

        return records;
    }
}
```

#### 3.5.2 成绩管理模块运行结果

**(1) 学生成绩查看**
- 学生可以查看个人所有考试记录
- 显示考试时间、题目数量、正确数、成绩、用时、等级、状态等信息
- 支持成绩记录的排序和筛选

*[学生成绩界面截图位置预留]*

**(2) 管理员成绩管理**
- 管理员可以查看所有学生的考试记录
- 提供成绩统计和分析功能
- 支持成绩数据的导出功能

*[管理员成绩管理界面截图位置预留]*

## 4 优化与改进

### (1) 调试过程中出现的问题与解决措施

**问题1：JDK 8兼容性问题**
- **问题描述**：代码中使用了`var`关键字（JDK 10+特性），导致JDK 8编译失败
- **解决措施**：将所有`var`关键字替换为显式类型声明，确保与JDK 8完全兼容

**问题2：数据库连接和加载异常**
- **问题描述**：题目列表和成绩列表加载时出现数据库连接异常
- **解决措施**：
  - 完善了数据库初始化脚本，添加了完整的表结构和示例数据
  - 改进了DatabaseUtil的错误处理和日志输出
  - 增加了数据库连接测试和详细的错误信息

**问题3：CRUD操作功能缺失**
- **问题描述**：题目管理的添加、编辑功能只有占位符，没有实际实现
- **解决措施**：
  - 创建了完整的QuestionDialog对话框，支持题目的添加和编辑
  - 实现了完整的题目CRUD操作流程
  - 添加了输入验证和错误处理机制

**问题4：数据映射和类型转换问题**
- **问题描述**：数据库查询结果映射到实体对象时出现类型转换错误
- **解决措施**：
  - 改进了mapResultSetToQuestion等映射方法的错误处理
  - 添加了空值检查和默认值设置
  - 完善了枚举类型的转换逻辑

**问题5：网络连接超时**
- **问题描述**：客户端连接服务器时偶尔出现超时
- **解决措施**：增加了连接重试机制和心跳包检测，提高了网络连接的稳定性

### (2) JDK 8兼容性优化

**语法兼容性修复**
- **修复内容**：将代码中的`var`关键字替换为显式类型声明
- **修复示例**：
```java
// 修复前（JDK 10+语法）
for (var detail : examRecord.getExamDetails()) {
    // 处理逻辑
}

// 修复后（JDK 8兼容）
for (ExamDetail detail : examRecord.getExamDetails()) {
    // 处理逻辑
}
```

**数据库操作优化**
- **改进内容**：增强了数据库连接的错误处理和日志输出
- **好处**：便于调试和问题定位，提高了系统的稳定性

### (3) 采用了新的开发工具及其好处

**Maven构建工具**
- **好处**：自动管理项目依赖，简化了项目构建和部署过程
- **应用**：统一管理MySQL驱动、JSON处理库等第三方依赖

**Gson JSON处理库**
- **好处**：简化了对象序列化和反序列化，提高了网络通信效率
- **应用**：用于客户端和服务器之间的消息传输

**数据库测试工具**
- **实现**：创建了DatabaseTest类用于验证数据库连接和基本操作
- **好处**：便于快速诊断数据库相关问题

### (4) 类的优化与改进

**用户权限管理优化**
- **改进前**：简单的用户类型判断
- **改进后**：引入了UserPermission枚举，实现了细粒度的权限控制
- **好处**：提高了系统的安全性和可扩展性

**消息传输机制优化**
- **改进前**：直接传输Java对象
- **改进后**：使用JSON格式进行消息传输
- **好处**：提高了系统的跨平台兼容性和可维护性

### (5) 系统结构的优化与改进

**MVC架构的严格实现**
- **Model层**：纯粹的数据模型，不包含业务逻辑
- **View层**：只负责界面展示和用户交互
- **Controller层**：处理业务逻辑和数据流转
- **好处**：提高了代码的可维护性和可测试性

**开闭原则的应用**
- **扩展性设计**：通过接口和抽象类实现功能扩展
- **配置化管理**：数据库连接、服务器端口等通过配置文件管理
- **好处**：便于系统功能的扩展和维护

### (6) 系统功能的改进与扩展

**文件IO功能扩展**
- **实现**：添加了题目导入导出、成绩报告生成、系统日志记录等功能
- **符合开闭原则**：通过FileUtil工具类实现，不修改现有代码
- **MVC结构**：文件操作作为独立的工具类，可被各层调用

**多线程并发优化**
- **实现**：服务器端使用线程池管理客户端连接
- **好处**：提高了系统的并发处理能力和稳定性

## 5 总结

### 实现的系统功能

本考试系统成功实现了以下核心功能：

1. **用户管理功能**：完整的用户注册、登录、权限管理体系
2. **题库管理功能**：管理员可以对试题进行增、删、改、查操作
3. **在线考试功能**：学生可以参加在线考试，支持随机抽题、时间控制、自动评分
4. **成绩管理功能**：自动计算和保存考试成绩，支持成绩查询和统计
5. **网络通信功能**：基于Socket实现的C/S架构，支持多用户并发访问
6. **数据持久化功能**：使用MySQL数据库存储所有业务数据

### 面向对象程序设计的应用

系统严格遵循面向对象设计原则：

**封装性**：每个类都有明确的职责边界，内部实现细节被隐藏
- 例如：User类封装了用户的所有属性和行为，外部只能通过公共方法访问

**继承性**：通过继承实现代码复用和功能扩展
- 例如：所有UI界面都继承自JFrame，复用了窗口的基本功能

**多态性**：通过接口和抽象类实现多态
- 例如：Message类的不同消息类型通过枚举实现多态处理

### MVC结构和开闭原则的遵循

**MVC架构的严格实现**：
- **Model层**：User、Question、ExamRecord等实体类，纯粹的数据模型
- **View层**：LoginFrame、AdminFrame、StudentFrame等界面类，只负责展示
- **Controller层**：UserController、QuestionController、ExamController等控制器类，处理业务逻辑

**开闭原则的体现**：
- **对扩展开放**：通过接口和配置文件，可以轻松添加新功能
- **对修改关闭**：新增功能不需要修改现有代码
- **实例**：添加新的题目类型只需扩展Question类，不需要修改现有的考试逻辑

### 系统特点和优势

1. **技术栈完整**：Java 8 + Swing + MySQL 8 + Socket + Maven
2. **架构清晰**：C/S架构结合MVC模式，层次分明
3. **功能完整**：涵盖了考试系统的所有核心功能
4. **扩展性强**：遵循开闭原则，便于功能扩展
5. **并发支持**：多线程处理，支持多用户同时使用
6. **用户体验好**：界面友好，操作简单直观

本系统成功实现了一个功能完整、架构清晰、可扩展的网上考试系统，满足了课程设计的所有技术要求和功能需求。

---

## 附录：系统部署和使用说明

### 环境要求
- JDK 8或以上版本
- MySQL 8.0数据库
- Maven 3.6或以上版本

### 部署步骤
1. 创建MySQL数据库，执行`src/main/resources/database/init.sql`初始化脚本
2. 修改`src/main/resources/config/database.properties`中的数据库连接配置
3. 使用Maven编译项目：`mvn clean compile`
4. 启动服务器：`mvn exec:java -Dexec.mainClass="com.exam.server.ExamServer"`
5. 启动客户端：`mvn exec:java -Dexec.mainClass="com.exam.client.ExamClient"`

### 默认账户
- 管理员：用户名`admin`，密码`admin123`
- 学生：用户名`student1`，密码`123456`

*[系统部署截图位置预留]*
