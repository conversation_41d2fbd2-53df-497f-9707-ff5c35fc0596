package com.exam.util;

import com.exam.model.Question;
import com.exam.model.User;
import com.exam.model.ExamRecord;
import com.exam.model.ExamDetail;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 文件操作工具类
 * 提供文件读写、数据导入导出等功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class FileUtil {
    
    // 文件编码
    private static final String FILE_ENCODING = "UTF-8";
    
    // 日期格式化器
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    // CSV分隔符
    private static final String CSV_SEPARATOR = ",";
    
    // 数据目录
    private static final String DATA_DIR = "data";
    
    /**
     * 确保数据目录存在
     */
    private static void ensureDataDirectory() {
        File dataDir = new File(DATA_DIR);
        if (!dataDir.exists()) {
            dataDir.mkdirs();
        }
    }
    
    /**
     * 读取文本文件内容
     * @param filePath 文件路径
     * @return 文件内容字符串
     * @throws IOException 文件读取异常
     */
    public static String readTextFile(String filePath) throws IOException {
        StringBuilder content = new StringBuilder();
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(filePath), FILE_ENCODING))) {
            
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        
        return content.toString();
    }
    
    /**
     * 写入文本文件
     * @param filePath 文件路径
     * @param content 文件内容
     * @throws IOException 文件写入异常
     */
    public static void writeTextFile(String filePath, String content) throws IOException {
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(filePath), FILE_ENCODING))) {
            writer.write(content);
        }
    }
    
    /**
     * 追加内容到文本文件
     * @param filePath 文件路径
     * @param content 要追加的内容
     * @throws IOException 文件写入异常
     */
    public static void appendTextFile(String filePath, String content) throws IOException {
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(filePath, true), FILE_ENCODING))) {
            writer.write(content);
            writer.newLine();
        }
    }
    
    /**
     * 导出题目到CSV文件
     * @param questions 题目列表
     * @param filePath 文件路径
     * @throws IOException 文件写入异常
     */
    public static void exportQuestionsToCSV(List<Question> questions, String filePath) throws IOException {
        ensureDataDirectory();
        
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(filePath), FILE_ENCODING))) {
            
            // 写入CSV头部
            writer.write("题目编号,题目内容,选项A,选项B,选项C,选项D,正确答案,难度,科目,创建时间");
            writer.newLine();
            
            // 写入题目数据
            for (Question question : questions) {
                StringBuilder line = new StringBuilder();
                line.append(question.getQuestionId()).append(CSV_SEPARATOR);
                line.append(escapeCSV(question.getQuestionContent())).append(CSV_SEPARATOR);
                line.append(escapeCSV(question.getOptionA())).append(CSV_SEPARATOR);
                line.append(escapeCSV(question.getOptionB())).append(CSV_SEPARATOR);
                line.append(escapeCSV(question.getOptionC())).append(CSV_SEPARATOR);
                line.append(escapeCSV(question.getOptionD())).append(CSV_SEPARATOR);
                line.append(question.getCorrectAnswer().getCode()).append(CSV_SEPARATOR);
                line.append(question.getDifficulty().getCode()).append(CSV_SEPARATOR);
                line.append(escapeCSV(question.getSubject())).append(CSV_SEPARATOR);
                line.append(question.getCreatedTime() != null ? 
                    DATE_FORMAT.format(question.getCreatedTime()) : "");
                
                writer.write(line.toString());
                writer.newLine();
            }
        }
        
        System.out.println("题目导出完成: " + filePath);
    }
    
    /**
     * 从CSV文件导入题目
     * @param filePath 文件路径
     * @return 题目列表
     * @throws IOException 文件读取异常
     */
    public static List<Question> importQuestionsFromCSV(String filePath) throws IOException {
        List<Question> questions = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(filePath), FILE_ENCODING))) {
            
            // 跳过头部行
            String headerLine = reader.readLine();
            if (headerLine == null) {
                throw new IOException("CSV文件为空");
            }
            
            String line;
            int lineNumber = 1;
            
            while ((line = reader.readLine()) != null) {
                lineNumber++;
                
                if (line.trim().isEmpty()) {
                    continue;
                }
                
                try {
                    Question question = parseQuestionFromCSV(line);
                    questions.add(question);
                } catch (Exception e) {
                    System.err.println("解析第" + lineNumber + "行失败: " + e.getMessage());
                }
            }
        }
        
        System.out.println("题目导入完成，共导入" + questions.size() + "道题目");
        return questions;
    }
    
    /**
     * 从CSV行解析题目对象
     * @param csvLine CSV行数据
     * @return 题目对象
     */
    private static Question parseQuestionFromCSV(String csvLine) {
        String[] fields = csvLine.split(CSV_SEPARATOR);
        
        if (fields.length < 9) {
            throw new IllegalArgumentException("CSV行数据不完整");
        }
        
        Question question = new Question();
        question.setQuestionContent(unescapeCSV(fields[1]));
        question.setOptionA(unescapeCSV(fields[2]));
        question.setOptionB(unescapeCSV(fields[3]));
        question.setOptionC(unescapeCSV(fields[4]));
        question.setOptionD(unescapeCSV(fields[5]));
        question.setCorrectAnswer(Question.Answer.fromCode(fields[6]));
        question.setDifficulty(Question.Difficulty.valueOf(fields[7]));
        question.setSubject(unescapeCSV(fields[8]));
        
        return question;
    }
    
    /**
     * 导出考试记录到CSV文件
     * @param examRecords 考试记录列表
     * @param filePath 文件路径
     * @throws IOException 文件写入异常
     */
    public static void exportExamRecordsToCSV(List<ExamRecord> examRecords, String filePath) throws IOException {
        ensureDataDirectory();
        
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(filePath), FILE_ENCODING))) {
            
            // 写入CSV头部
            writer.write("记录编号,用户编号,用户姓名,考试时间,总题数,正确答案数,成绩,用时(秒),考试状态");
            writer.newLine();
            
            // 写入考试记录数据
            for (ExamRecord record : examRecords) {
                StringBuilder line = new StringBuilder();
                line.append(record.getRecordId()).append(CSV_SEPARATOR);
                line.append(record.getUserId()).append(CSV_SEPARATOR);
                line.append(escapeCSV(record.getUserName())).append(CSV_SEPARATOR);
                line.append(record.getExamTime() != null ? 
                    DATE_FORMAT.format(record.getExamTime()) : "").append(CSV_SEPARATOR);
                line.append(record.getTotalQuestions()).append(CSV_SEPARATOR);
                line.append(record.getCorrectAnswers()).append(CSV_SEPARATOR);
                line.append(record.getScore()).append(CSV_SEPARATOR);
                line.append(record.getTimeUsed()).append(CSV_SEPARATOR);
                line.append(record.getStatus().getCode());
                
                writer.write(line.toString());
                writer.newLine();
            }
        }
        
        System.out.println("考试记录导出完成: " + filePath);
    }
    
    /**
     * 生成考试报告
     * @param examRecord 考试记录
     * @param filePath 文件路径
     * @throws IOException 文件写入异常
     */
    public static void generateExamReport(ExamRecord examRecord, String filePath) throws IOException {
        ensureDataDirectory();
        
        StringBuilder report = new StringBuilder();
        report.append("=== 考试成绩报告 ===\n\n");
        report.append("考生姓名: ").append(examRecord.getUserName()).append("\n");
        report.append("考试时间: ").append(DATE_FORMAT.format(examRecord.getExamTime())).append("\n");
        report.append("总题数: ").append(examRecord.getTotalQuestions()).append("\n");
        report.append("正确答案数: ").append(examRecord.getCorrectAnswers()).append("\n");
        report.append("考试成绩: ").append(examRecord.getScore()).append("分\n");
        report.append("用时: ").append(examRecord.getFormattedDuration()).append("\n");
        report.append("成绩等级: ").append(examRecord.getGradeLevel()).append("\n");
        report.append("考试状态: ").append(examRecord.getStatus().getDescription()).append("\n\n");
        
        // 添加详细答题情况
        if (examRecord.getExamDetails() != null && !examRecord.getExamDetails().isEmpty()) {
            report.append("=== 详细答题情况 ===\n\n");
            int questionNum = 1;
            for (ExamDetail detail : examRecord.getExamDetails()) {
                report.append("第").append(questionNum++).append("题: ");
                report.append(detail.getResultDescription()).append(" ");
                report.append(detail.getStatusIcon()).append("\n");
                
                if (detail.getQuestion() != null) {
                    report.append("题目: ").append(detail.getQuestion().getQuestionContent()).append("\n");
                    report.append("您的答案: ").append(detail.getUserAnswerText()).append("\n");
                    report.append("正确答案: ").append(detail.getCorrectAnswerText()).append("\n");
                }
                report.append("\n");
            }
        }
        
        writeTextFile(filePath, report.toString());
        System.out.println("考试报告生成完成: " + filePath);
    }
    
    /**
     * 记录系统日志
     * @param level 日志级别
     * @param message 日志消息
     */
    public static void logMessage(String level, String message) {
        try {
            ensureDataDirectory();
            String logFile = DATA_DIR + File.separator + "system.log";
            String timestamp = DATE_FORMAT.format(new Date());
            String logEntry = String.format("[%s] %s: %s", timestamp, level, message);
            appendTextFile(logFile, logEntry);
        } catch (IOException e) {
            System.err.println("写入日志失败: " + e.getMessage());
        }
    }
    
    /**
     * 转义CSV字段中的特殊字符
     * @param field 字段内容
     * @return 转义后的内容
     */
    private static String escapeCSV(String field) {
        if (field == null) {
            return "";
        }
        
        // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
            return "\"" + field.replace("\"", "\"\"") + "\"";
        }
        
        return field;
    }
    
    /**
     * 反转义CSV字段
     * @param field 字段内容
     * @return 反转义后的内容
     */
    private static String unescapeCSV(String field) {
        if (field == null) {
            return "";
        }
        
        // 移除首尾引号并反转义内部引号
        if (field.startsWith("\"") && field.endsWith("\"")) {
            field = field.substring(1, field.length() - 1);
            field = field.replace("\"\"", "\"");
        }
        
        return field;
    }
    
    /**
     * 检查文件是否存在
     * @param filePath 文件路径
     * @return true如果文件存在，否则false
     */
    public static boolean fileExists(String filePath) {
        return new File(filePath).exists();
    }
    
    /**
     * 创建目录
     * @param dirPath 目录路径
     * @return true如果创建成功，否则false
     */
    public static boolean createDirectory(String dirPath) {
        File dir = new File(dirPath);
        return dir.exists() || dir.mkdirs();
    }
    
    /**
     * 删除文件
     * @param filePath 文件路径
     * @return true如果删除成功，否则false
     */
    public static boolean deleteFile(String filePath) {
        File file = new File(filePath);
        return file.exists() && file.delete();
    }
}
