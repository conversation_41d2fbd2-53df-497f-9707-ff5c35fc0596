package com.exam.client;

import com.exam.client.ui.LoginFrame;
import com.exam.model.Message;
import com.exam.model.User;
import com.exam.util.NetworkUtil;

import javax.swing.*;
import java.io.IOException;
import java.net.Socket;

/**
 * 考试系统客户端主类
 * 实现C/S架构的客户端
 * 管理与服务器的连接和用户界面
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ExamClient {
    
    // 服务器连接配置
    private static final String DEFAULT_HOST = "localhost";
    private static final int DEFAULT_PORT = 8888;
    
    // 网络连接
    private Socket socket;
    private String serverHost;
    private int serverPort;
    
    // 用户信息
    private User currentUser;
    private String sessionId;
    
    // 连接状态
    private boolean isConnected = false;
    
    /**
     * 构造函数
     * @param host 服务器地址
     * @param port 服务器端口
     */
    public ExamClient(String host, int port) {
        this.serverHost = host;
        this.serverPort = port;
        
        // 设置系统外观
        setupLookAndFeel();
    }
    
    /**
     * 设置系统外观
     */
    private void setupLookAndFeel() {
        try {
            // 使用系统默认外观
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
        } catch (Exception e) {
            System.err.println("设置系统外观失败: " + e.getMessage());
        }
    }
    
    /**
     * 连接到服务器
     * @return true如果连接成功，否则false
     */
    public boolean connectToServer() {
        try {
            socket = new Socket(serverHost, serverPort);
            isConnected = true;
            
            System.out.println("成功连接到服务器: " + serverHost + ":" + serverPort);
            return true;
            
        } catch (IOException e) {
            System.err.println("连接服务器失败: " + e.getMessage());
            isConnected = false;
            return false;
        }
    }
    
    /**
     * 断开与服务器的连接
     */
    public void disconnect() {
        try {
            if (currentUser != null) {
                // 发送登出请求
                Message logoutRequest = new Message(Message.MessageType.LOGOUT_REQUEST);
                sendMessage(logoutRequest);
            }
            
            NetworkUtil.closeSocket(socket);
            isConnected = false;
            currentUser = null;
            sessionId = null;
            
            System.out.println("已断开与服务器的连接");
            
        } catch (Exception e) {
            System.err.println("断开连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送消息到服务器（线程安全）
     * @param message 要发送的消息
     * @return 服务器响应消息
     * @throws IOException 网络IO异常
     */
    public synchronized Message sendMessage(Message message) throws IOException {
        if (!isConnected || socket == null) {
            throw new IOException("未连接到服务器");
        }

        // 设置用户信息
        if (currentUser != null) {
            message.setUserId(currentUser.getUserId());
        }
        if (sessionId != null) {
            message.setSessionId(sessionId);
        }

        Message response = NetworkUtil.sendRequestAndWaitResponse(message, socket);

        return response;
    }
    
    /**
     * 用户登录
     * @param username 用户名
     * @param password 密码
     * @return 登录结果消息
     */
    public Message login(String username, String password) {
        try {
            Message loginRequest = new Message(Message.MessageType.LOGIN_REQUEST);
            loginRequest.addParam("username", username);
            loginRequest.addParam("password", password);
            
            Message response = sendMessage(loginRequest);
            
            if (response.isSuccess()) {
                // 登录成功，保存用户信息
                this.currentUser = NetworkUtil.fromJson(
                    NetworkUtil.toJson(response.getData()), User.class);
                this.sessionId = response.getSessionId();
                
                System.out.println("登录成功: " + currentUser.getRealName());
            }
            
            return response;
            
        } catch (IOException e) {
            System.err.println("登录请求失败: " + e.getMessage());
            Message errorResponse = new Message(Message.MessageType.LOGIN_RESPONSE);
            errorResponse.setStatus(Message.MessageStatus.ERROR);
            errorResponse.setMessage("网络连接失败: " + e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 用户注册
     * @param user 用户信息
     * @return 注册结果消息
     */
    public Message register(User user) {
        try {
            Message registerRequest = new Message(Message.MessageType.REGISTER_REQUEST, user);
            return sendMessage(registerRequest);
            
        } catch (IOException e) {
            System.err.println("注册请求失败: " + e.getMessage());
            Message errorResponse = new Message(Message.MessageType.REGISTER_RESPONSE);
            errorResponse.setStatus(Message.MessageStatus.ERROR);
            errorResponse.setMessage("网络连接失败: " + e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 启动客户端应用程序
     */
    public void start() {
        // 尝试连接服务器
        if (!connectToServer()) {
            JOptionPane.showMessageDialog(null, 
                "无法连接到服务器 " + serverHost + ":" + serverPort + "\n请检查服务器是否启动", 
                "连接失败", 
                JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        // 显示登录界面
        SwingUtilities.invokeLater(() -> {
            LoginFrame loginFrame = new LoginFrame(this);
            loginFrame.setVisible(true);
        });
    }
    
    /**
     * 检查是否已连接到服务器
     * @return true如果已连接，否则false
     */
    public boolean isConnected() {
        return isConnected && socket != null && !socket.isClosed();
    }
    
    /**
     * 检查用户是否已登录
     * @return true如果已登录，否则false
     */
    public boolean isLoggedIn() {
        return currentUser != null;
    }
    
    /**
     * 获取当前用户
     * @return 当前用户对象
     */
    public User getCurrentUser() {
        return currentUser;
    }
    
    /**
     * 获取会话ID
     * @return 会话ID
     */
    public String getSessionId() {
        return sessionId;
    }
    
    /**
     * 获取服务器地址
     * @return 服务器地址
     */
    public String getServerHost() {
        return serverHost;
    }
    
    /**
     * 获取服务器端口
     * @return 服务器端口
     */
    public int getServerPort() {
        return serverPort;
    }
    
    /**
     * 发送心跳包
     */
    public void sendHeartbeat() {
        try {
            if (isConnected() && currentUser != null) {
                Message heartbeat = NetworkUtil.createHeartbeatMessage(
                    currentUser.getUserId(), sessionId);
                sendMessage(heartbeat);
            }
        } catch (IOException e) {
            System.err.println("发送心跳包失败: " + e.getMessage());
        }
    }
    
    /**
     * 启动心跳线程
     */
    public void startHeartbeat() {
        Thread heartbeatThread = new Thread(() -> {
            while (isConnected()) {
                try {
                    Thread.sleep(30000); // 每30秒发送一次心跳
                    sendHeartbeat();
                } catch (InterruptedException e) {
                    break;
                } catch (Exception e) {
                    System.err.println("心跳线程异常: " + e.getMessage());
                }
            }
        });
        
        heartbeatThread.setDaemon(true);
        heartbeatThread.start();
    }
    
    /**
     * 主方法
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 设置系统属性
        System.setProperty("file.encoding", "UTF-8");
        
        // 解析命令行参数
        String host = DEFAULT_HOST;
        int port = DEFAULT_PORT;
        
        if (args.length >= 1) {
            host = args[0];
        }
        if (args.length >= 2) {
            try {
                port = Integer.parseInt(args[1]);
            } catch (NumberFormatException e) {
                System.err.println("无效的端口号: " + args[1]);
                return;
            }
        }
        
        // 创建并启动客户端
        ExamClient client = new ExamClient(host, port);
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("客户端正在关闭...");
            client.disconnect();
        }));
        
        // 启动客户端
        client.start();
    }
}
