package com.exam.client.ui;

import com.exam.client.ExamClient;
import com.exam.controller.QuestionController;
import com.exam.controller.UserController;
import com.exam.controller.ExamController;
import com.exam.model.Message;
import com.exam.model.Question;
import com.exam.model.ExamRecord;
import com.exam.util.ExamConfig;
import com.exam.util.NetworkUtil;
import com.google.gson.reflect.TypeToken;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * 管理员主界面
 * 实现MVC模式中的View层
 * 提供题目管理和成绩查看功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class AdminFrame extends JFrame {
    
    private ExamClient client;
    private UserController userController;
    private QuestionController questionController;
    private ExamController examController;
    
    // UI组件
    private JTabbedPane tabbedPane;
    private JTable questionTable;
    private JTable scoreTable;
    private DefaultTableModel questionTableModel;
    private DefaultTableModel scoreTableModel;
    private JLabel statusLabel;
    private JLabel userInfoLabel;
    private JLabel durationValueLabel; // 添加配置显示标签的引用
    
    /**
     * 构造函数
     * @param client 客户端对象
     */
    public AdminFrame(ExamClient client) {
        this.client = client;
        this.userController = new UserController(client);
        this.questionController = new QuestionController(client);
        this.examController = new ExamController(client);
        
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        setupFrame();
        loadData();
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeComponents() {
        // 创建选项卡面板
        tabbedPane = new JTabbedPane();
        
        // 创建题目管理表格
        String[] questionColumns = {"题目ID", "题目内容", "正确答案", "难度", "科目", "创建时间"};
        questionTableModel = new DefaultTableModel(questionColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        questionTable = new JTable(questionTableModel);
        questionTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        // 创建成绩管理表格
        String[] scoreColumns = {"记录ID", "学生姓名", "考试时间", "总题数", "正确数", "成绩", "用时", "状态"};
        scoreTableModel = new DefaultTableModel(scoreColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        scoreTable = new JTable(scoreTableModel);
        scoreTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        // 创建状态标签
        statusLabel = new JLabel("就绪");
        statusLabel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        // 创建用户信息标签
        userInfoLabel = new JLabel();
        updateUserInfo();
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // 顶部面板
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 5, 10));
        
        JLabel titleLabel = new JLabel("考试系统 - 管理员控制台");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 16));
        topPanel.add(titleLabel, BorderLayout.WEST);
        topPanel.add(userInfoLabel, BorderLayout.EAST);
        
        add(topPanel, BorderLayout.NORTH);
        
        // 创建题目管理面板
        JPanel questionPanel = createQuestionManagementPanel();
        tabbedPane.addTab("题目管理", questionPanel);
        
        // 创建成绩管理面板
        JPanel scorePanel = createScoreManagementPanel();
        tabbedPane.addTab("成绩管理", scorePanel);

        // 创建系统配置面板
        JPanel configPanel = createSystemConfigPanel();
        tabbedPane.addTab("系统配置", configPanel);
        
        add(tabbedPane, BorderLayout.CENTER);
        
        // 底部状态栏
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.add(statusLabel, BorderLayout.WEST);
        
        JButton logoutButton = new JButton("退出登录");
        logoutButton.addActionListener(e -> logout());
        statusPanel.add(logoutButton, BorderLayout.EAST);
        
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建题目管理面板
     */
    private JPanel createQuestionManagementPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        // 工具栏
        JPanel toolBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        
        JButton addButton = new JButton("添加题目");
        JButton editButton = new JButton("编辑题目");
        JButton deleteButton = new JButton("删除题目");
        JButton refreshButton = new JButton("刷新");
        
        addButton.addActionListener(e -> showAddQuestionDialog());
        editButton.addActionListener(e -> showEditQuestionDialog());
        deleteButton.addActionListener(e -> deleteSelectedQuestion());
        refreshButton.addActionListener(e -> loadQuestions());
        
        toolBar.add(addButton);
        toolBar.add(editButton);
        toolBar.add(deleteButton);
        toolBar.add(new JSeparator(SwingConstants.VERTICAL));
        toolBar.add(refreshButton);
        
        panel.add(toolBar, BorderLayout.NORTH);
        
        // 表格
        JScrollPane scrollPane = new JScrollPane(questionTable);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建成绩管理面板
     */
    private JPanel createScoreManagementPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        // 工具栏 - 移除导出功能
        JPanel toolBar = new JPanel(new FlowLayout(FlowLayout.LEFT));

        JButton refreshButton = new JButton("刷新");
        refreshButton.addActionListener(e -> loadScores());

        toolBar.add(refreshButton);
        
        panel.add(toolBar, BorderLayout.NORTH);
        
        // 表格
        JScrollPane scrollPane = new JScrollPane(scoreTable);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 窗口关闭事件
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        addWindowListener(new java.awt.event.WindowAdapter() {
            @Override
            public void windowClosing(java.awt.event.WindowEvent e) {
                logout();
            }
        });
    }
    
    /**
     * 设置窗口属性
     */
    private void setupFrame() {
        setTitle("考试系统 - 管理员 - 学号: 3240608005 姓名: 黄雁");
        setSize(1000, 700);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
    }
    
    /**
     * 加载数据
     */
    private void loadData() {
        loadQuestions();
        loadScores();
    }
    
    /**
     * 加载题目列表
     */
    private void loadQuestions() {
        SwingWorker<Message, Void> worker = new SwingWorker<Message, Void>() {
            @Override
            protected Message doInBackground() throws Exception {
                return questionController.getAllQuestions();
            }
            
            @Override
            protected void done() {
                try {
                    Message response = get();
                    if (response.isSuccess()) {
                        // 使用安全的类型转换方法
                        TypeToken<List<Question>> typeToken = new TypeToken<List<Question>>() {};
                        List<Question> questions = NetworkUtil.extractDataSafely(response.getData(), typeToken);

                        if (questions != null) {
                            updateQuestionTable(questions);
                            setStatus("题目列表加载完成，共" + questions.size() + "道题目");
                        } else {
                            setStatus("题目数据格式错误");
                            JOptionPane.showMessageDialog(AdminFrame.this,
                                "题目数据格式错误，请检查服务器配置",
                                "数据错误", JOptionPane.ERROR_MESSAGE);
                        }
                    } else {
                        setStatus("加载题目失败: " + response.getMessage());
                        JOptionPane.showMessageDialog(AdminFrame.this,
                            "加载题目失败: " + response.getMessage(),
                            "错误", JOptionPane.ERROR_MESSAGE);
                    }
                } catch (Exception e) {
                    setStatus("加载题目异常: " + e.getMessage());
                    e.printStackTrace();
                    JOptionPane.showMessageDialog(AdminFrame.this,
                        "加载题目异常: " + e.getMessage(),
                        "错误", JOptionPane.ERROR_MESSAGE);
                }
            }
        };
        worker.execute();
    }
    
    /**
     * 加载成绩列表
     */
    private void loadScores() {
        SwingWorker<Message, Void> worker = new SwingWorker<Message, Void>() {
            @Override
            protected Message doInBackground() throws Exception {
                return examController.getScoreList();
            }
            
            @Override
            protected void done() {
                try {
                    Message response = get();
                    if (response.isSuccess()) {
                        // 使用安全的类型转换方法
                        TypeToken<List<ExamRecord>> typeToken = new TypeToken<List<ExamRecord>>() {};
                        List<ExamRecord> records = NetworkUtil.extractDataSafely(response.getData(), typeToken);

                        if (records != null) {
                            updateScoreTable(records);
                            setStatus("成绩列表加载完成，共" + records.size() + "条记录");
                        } else {
                            setStatus("成绩数据格式错误");
                            JOptionPane.showMessageDialog(AdminFrame.this,
                                "成绩数据格式错误，请检查服务器配置",
                                "数据错误", JOptionPane.ERROR_MESSAGE);
                        }
                    } else {
                        setStatus("加载成绩失败: " + response.getMessage());
                        JOptionPane.showMessageDialog(AdminFrame.this,
                            "加载成绩失败: " + response.getMessage(),
                            "加载失败", JOptionPane.ERROR_MESSAGE);
                    }
                } catch (Exception e) {
                    setStatus("加载成绩异常: " + e.getMessage());
                    e.printStackTrace();
                    JOptionPane.showMessageDialog(AdminFrame.this,
                        "加载成绩异常: " + e.getMessage(),
                        "系统异常", JOptionPane.ERROR_MESSAGE);
                }
            }
        };
        worker.execute();
    }
    
    /**
     * 更新题目表格
     */
    private void updateQuestionTable(List<Question> questions) {
        questionTableModel.setRowCount(0);

        if (questions == null || questions.isEmpty()) {
            System.out.println("题目列表为空");
            return;
        }

        for (Question question : questions) {
            if (question == null) {
                System.err.println("发现空的题目对象，跳过");
                continue;
            }

            Integer questionId = question.getQuestionId();
            if (questionId == null) {
                System.err.println("题目ID为空，跳过题目: " + question.getQuestionContent());
                continue;
            }

            Object[] row = {
                questionId,  // 确保ID不为空
                question.getQuestionContent() != null && question.getQuestionContent().length() > 50 ?
                    question.getQuestionContent().substring(0, 50) + "..." :
                    question.getQuestionContent(),
                question.getCorrectAnswer() != null ? question.getCorrectAnswer().getCode() : "",
                question.getDifficulty() != null ? question.getDifficulty().getDescription() : "",
                question.getSubject() != null ? question.getSubject() : "",
                question.getCreatedTime()
            };

            questionTableModel.addRow(row);
        }
    }
    
    /**
     * 更新成绩表格
     */
    private void updateScoreTable(List<ExamRecord> records) {
        scoreTableModel.setRowCount(0);
        for (ExamRecord record : records) {
            Object[] row = {
                record.getRecordId(),
                record.getUserName(),
                record.getExamTime(),
                record.getTotalQuestions(),
                record.getCorrectAnswers(),
                record.getScore() + "分",
                examController.formatTime(record.getTimeUsed()),
                record.getStatus().getDescription()
            };
            scoreTableModel.addRow(row);
        }
    }
    
    /**
     * 显示添加题目对话框
     */
    private void showAddQuestionDialog() {
        QuestionDialog dialog = new QuestionDialog(this);
        dialog.setVisible(true);

        if (dialog.isConfirmed()) {
            Question newQuestion = dialog.getQuestion();

            SwingWorker<Message, Void> worker = new SwingWorker<Message, Void>() {
                @Override
                protected Message doInBackground() throws Exception {
                    return questionController.addQuestion(newQuestion);
                }

                @Override
                protected void done() {
                    try {
                        Message response = get();
                        if (response.isSuccess()) {
                            setStatus("题目添加成功");
                            loadQuestions(); // 重新加载题目列表
                            JOptionPane.showMessageDialog(AdminFrame.this,
                                "题目添加成功！", "成功", JOptionPane.INFORMATION_MESSAGE);
                        } else {
                            setStatus("添加题目失败: " + response.getMessage());
                            JOptionPane.showMessageDialog(AdminFrame.this,
                                "添加题目失败: " + response.getMessage(),
                                "错误", JOptionPane.ERROR_MESSAGE);
                        }
                    } catch (Exception e) {
                        setStatus("添加题目异常: " + e.getMessage());
                        JOptionPane.showMessageDialog(AdminFrame.this,
                            "添加题目异常: " + e.getMessage(),
                            "错误", JOptionPane.ERROR_MESSAGE);
                    }
                }
            };
            worker.execute();
        }
    }
    
    /**
     * 显示编辑题目对话框
     */
    private void showEditQuestionDialog() {
        int selectedRow = questionTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "请选择要编辑的题目", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        // 获取选中的题目ID
        Integer questionId = (Integer) questionTableModel.getValueAt(selectedRow, 0);

        // 从当前显示的数据中构建Question对象
        Question selectedQuestion = new Question();
        selectedQuestion.setQuestionId(questionId);
        selectedQuestion.setQuestionContent((String) questionTableModel.getValueAt(selectedRow, 1));
        // 注意：这里需要从完整的题目数据中获取，简化处理先创建一个基本的Question对象

        // 为了获取完整的题目信息，我们需要重新从服务器获取
        SwingWorker<Question, Void> fetchWorker = new SwingWorker<Question, Void>() {
            @Override
            protected Question doInBackground() throws Exception {
                // 这里应该调用获取单个题目的方法，简化处理，我们使用现有数据
                Question question = new Question();
                question.setQuestionId(questionId);
                question.setQuestionContent((String) questionTableModel.getValueAt(selectedRow, 1));
                question.setCorrectAnswer(Question.Answer.fromCode((String) questionTableModel.getValueAt(selectedRow, 2)));
                // 映射难度描述到枚举
                String difficultyDesc = (String) questionTableModel.getValueAt(selectedRow, 3);
                Question.Difficulty difficulty = Question.Difficulty.MEDIUM; // 默认值
                for (Question.Difficulty d : Question.Difficulty.values()) {
                    if (d.getDescription().equals(difficultyDesc)) {
                        difficulty = d;
                        break;
                    }
                }
                question.setDifficulty(difficulty);
                question.setSubject((String) questionTableModel.getValueAt(selectedRow, 4));

                // 设置默认选项（实际应用中应该从数据库获取）
                question.setOptionA("选项A");
                question.setOptionB("选项B");
                question.setOptionC("选项C");
                question.setOptionD("选项D");

                return question;
            }

            @Override
            protected void done() {
                try {
                    Question question = get();

                    QuestionDialog dialog = new QuestionDialog(AdminFrame.this, question);
                    dialog.setVisible(true);

                    if (dialog.isConfirmed()) {
                        Question updatedQuestion = dialog.getQuestion();

                        SwingWorker<Message, Void> updateWorker = new SwingWorker<Message, Void>() {
                            @Override
                            protected Message doInBackground() throws Exception {
                                return questionController.updateQuestion(updatedQuestion);
                            }

                            @Override
                            protected void done() {
                                try {
                                    Message response = get();
                                    if (response.isSuccess()) {
                                        setStatus("题目更新成功");
                                        loadQuestions(); // 重新加载题目列表
                                        JOptionPane.showMessageDialog(AdminFrame.this,
                                            "题目更新成功！", "成功", JOptionPane.INFORMATION_MESSAGE);
                                    } else {
                                        setStatus("更新题目失败: " + response.getMessage());
                                        JOptionPane.showMessageDialog(AdminFrame.this,
                                            "更新题目失败: " + response.getMessage(),
                                            "错误", JOptionPane.ERROR_MESSAGE);
                                    }
                                } catch (Exception e) {
                                    setStatus("更新题目异常: " + e.getMessage());
                                    JOptionPane.showMessageDialog(AdminFrame.this,
                                        "更新题目异常: " + e.getMessage(),
                                        "错误", JOptionPane.ERROR_MESSAGE);
                                }
                            }
                        };
                        updateWorker.execute();
                    }

                } catch (Exception e) {
                    setStatus("获取题目信息失败: " + e.getMessage());
                    JOptionPane.showMessageDialog(AdminFrame.this,
                        "获取题目信息失败: " + e.getMessage(),
                        "错误", JOptionPane.ERROR_MESSAGE);
                }
            }
        };
        fetchWorker.execute();
    }
    
    /**
     * 删除选中的题目
     */
    private void deleteSelectedQuestion() {
        int selectedRow = questionTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "请选择要删除的题目", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        if (selectedRow >= questionTableModel.getRowCount()) {
            JOptionPane.showMessageDialog(this, "选中的行超出表格范围", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }

        int confirm = JOptionPane.showConfirmDialog(this,
            "确定要删除选中的题目吗？", "确认删除",
            JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);

        if (confirm == JOptionPane.YES_OPTION) {
            try {
                // 获取题目ID，增加详细的调试信息
                Object questionIdObj = questionTableModel.getValueAt(selectedRow, 0);

                Integer questionId = null;

                // 安全的类型转换
                if (questionIdObj == null) {
                    JOptionPane.showMessageDialog(this,
                        "题目ID为空，可能数据未正确加载。请刷新题目列表后重试。",
                        "错误", JOptionPane.ERROR_MESSAGE);
                    return;
                } else if (questionIdObj instanceof Integer) {
                    questionId = (Integer) questionIdObj;
                } else if (questionIdObj instanceof String) {
                    try {
                        questionId = Integer.parseInt((String) questionIdObj);
                    } catch (NumberFormatException e) {
                        JOptionPane.showMessageDialog(this,
                            "题目ID格式错误: " + questionIdObj,
                            "错误", JOptionPane.ERROR_MESSAGE);
                        return;
                    }
                } else if (questionIdObj instanceof Number) {
                    questionId = ((Number) questionIdObj).intValue();
                } else {
                    JOptionPane.showMessageDialog(this,
                        "无法获取题目ID，数据类型错误: " + questionIdObj.getClass().getName() +
                        "\n值: " + questionIdObj,
                        "错误", JOptionPane.ERROR_MESSAGE);
                    return;
                }

                if (questionId == null || questionId <= 0) {
                    JOptionPane.showMessageDialog(this,
                        "题目ID无效: " + questionId + "，无法删除",
                        "错误", JOptionPane.ERROR_MESSAGE);
                    return;
                }

                final Integer finalQuestionId = questionId;

                SwingWorker<Message, Void> worker = new SwingWorker<Message, Void>() {
                    @Override
                    protected Message doInBackground() throws Exception {
                        return questionController.deleteQuestion(finalQuestionId);
                    }

                    @Override
                    protected void done() {
                        try {
                            Message response = get();
                            if (response.isSuccess()) {
                                setStatus("题目删除成功");
                                loadQuestions();
                                JOptionPane.showMessageDialog(AdminFrame.this,
                                    "题目删除成功！", "成功", JOptionPane.INFORMATION_MESSAGE);
                            } else {
                                setStatus("删除题目失败: " + response.getMessage());
                                JOptionPane.showMessageDialog(AdminFrame.this,
                                    "删除题目失败: " + response.getMessage(),
                                    "错误", JOptionPane.ERROR_MESSAGE);
                            }
                        } catch (Exception e) {
                            setStatus("删除题目异常: " + e.getMessage());
                            JOptionPane.showMessageDialog(AdminFrame.this,
                                "删除题目异常: " + e.getMessage(),
                                "错误", JOptionPane.ERROR_MESSAGE);
                        }
                    }
                };
                worker.execute();

            } catch (Exception e) {
                JOptionPane.showMessageDialog(this,
                    "获取题目信息失败: " + e.getMessage(),
                    "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * 创建系统配置面板
     */
    private JPanel createSystemConfigPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // 配置项面板
        JPanel configPanel = new JPanel(new GridBagLayout());
        configPanel.setBorder(BorderFactory.createTitledBorder("考试配置"));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);

        // 当前考试时长显示
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel currentDurationLabel = new JLabel("当前考试时长:");
        currentDurationLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        configPanel.add(currentDurationLabel, gbc);

        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        int currentDuration = ExamConfig.getInstance().getDefaultExamDurationMinutes();
        durationValueLabel = new JLabel(currentDuration + " 分钟"); // 使用实例变量
        durationValueLabel.setFont(new Font("微软雅黑", Font.BOLD, 14));
        durationValueLabel.setForeground(Color.BLUE);
        configPanel.add(durationValueLabel, gbc);

        // 修改按钮
        gbc.gridx = 2;
        JButton configButton = new JButton("修改配置");
        configButton.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        configButton.addActionListener(e -> showExamConfigDialog());
        configPanel.add(configButton, gbc);

        // 说明文字
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.gridwidth = 3;
        gbc.anchor = GridBagConstraints.CENTER;
        JLabel descLabel = new JLabel("此配置将应用于所有学生的考试时长");
        descLabel.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        descLabel.setForeground(Color.GRAY);
        configPanel.add(descLabel, gbc);

        panel.add(configPanel, BorderLayout.NORTH);

        return panel;
    }

    /**
     * 显示考试配置对话框
     */
    private void showExamConfigDialog() {
        int currentDuration = ExamConfig.getInstance().getDefaultExamDurationMinutes();
        ExamConfigDialog dialog = new ExamConfigDialog(this, currentDuration);
        dialog.setVisible(true);

        if (dialog.isConfirmed()) {
            int newDuration = dialog.getExamDurationMinutes();
            try {
                // 保存配置
                ExamConfig.getInstance().setDefaultExamDurationMinutes(newDuration);

                JOptionPane.showMessageDialog(this,
                    String.format("考试时长已更新为 %d 分钟\n此配置将应用于所有新的考试", newDuration),
                    "配置更新成功", JOptionPane.INFORMATION_MESSAGE);

                // 更新显示
                setStatus("考试时长配置已更新为 " + newDuration + " 分钟");

                // 刷新配置面板显示
                refreshConfigurationDisplay();

            } catch (Exception e) {
                JOptionPane.showMessageDialog(this,
                    "保存配置失败: " + e.getMessage(),
                    "配置错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * 刷新配置显示
     */
    private void refreshConfigurationDisplay() {
        if (durationValueLabel != null) {
            int currentDuration = ExamConfig.getInstance().getDefaultExamDurationMinutes();
            durationValueLabel.setText(currentDuration + " 分钟");
            durationValueLabel.repaint();
        }
    }


    /**
     * 退出登录
     */
    private void logout() {
        int option = JOptionPane.showConfirmDialog(this,
            "确定要退出登录吗？", "确认退出",
            JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
        
        if (option == JOptionPane.YES_OPTION) {
            userController.logout();
            client.disconnect();
            
            SwingUtilities.invokeLater(() -> {
                LoginFrame loginFrame = new LoginFrame(client);
                loginFrame.setVisible(true);
                dispose();
            });
        }
    }
    
    /**
     * 更新用户信息
     */
    private void updateUserInfo() {
        if (userInfoLabel != null) {
            userInfoLabel.setText(userController.getStudentIdentificationDisplay());
            userInfoLabel.setFont(new Font("微软雅黑", Font.BOLD, 14));
            userInfoLabel.setForeground(new Color(0, 102, 204)); // 蓝色显示
        }
    }
    
    /**
     * 设置状态信息
     */
    private void setStatus(String status) {
        if (statusLabel != null) {
            statusLabel.setText(status);
        }
    }
}
