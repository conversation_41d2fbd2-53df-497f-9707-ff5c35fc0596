package com.exam.client.ui;

import com.exam.client.ExamClient;
import com.exam.controller.ExamController;
import com.exam.model.*;
import com.exam.util.NetworkUtil;
import com.google.gson.reflect.TypeToken;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.List;

/**
 * 考试界面
 * 实现MVC模式中的View层
 * 提供考试答题功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ExamFrame extends JFrame {
    
    private ExamClient client;
    private ExamController examController;
    private List<Question> questions;
    private List<Question.Answer> userAnswers;
    private int currentQuestionIndex;
    private int examDuration;
    private int timeRemaining;
    private Timer examTimer;
    
    // UI组件
    private JLabel questionLabel;
    private JRadioButton optionA, optionB, optionC, optionD;
    private ButtonGroup optionGroup;
    private JButton prevButton, nextButton, submitButton;
    private JLabel timeLabel;
    private JLabel progressLabel;
    private JLabel studentInfoLabel;
    private JList<String> questionList;
    private DefaultListModel<String> questionListModel;
    
    /**
     * 构造函数
     * @param client 客户端对象
     * @param examResponse 考试响应消息
     * @param examDuration 考试时长（秒）
     */
    public ExamFrame(ExamClient client, Message examResponse, int examDuration) {
        this.client = client;
        this.examController = new ExamController(client);
        this.examDuration = examDuration;
        this.timeRemaining = examDuration;
        this.currentQuestionIndex = 0;
        
        List<Question> examQuestions = null;

        try {
            // 首先尝试直接转换
            if (examResponse.getData() instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> rawList = (List<Object>) examResponse.getData();
                examQuestions = new ArrayList<>();

                for (Object item : rawList) {
                    Question question = NetworkUtil.extractDataSafely(item, Question.class);
                    if (question != null) {
                        examQuestions.add(question);
                    } else {
                        System.err.println("ExamFrame: 无法转换题目对象: " + item);
                    }
                }
            } else {
                // 使用TypeToken方式
                TypeToken<List<Question>> typeToken = new TypeToken<List<Question>>() {};
                examQuestions = NetworkUtil.extractDataSafely(examResponse.getData(), typeToken);
            }
        } catch (Exception e) {
            System.err.println("ExamFrame: 解析题目数据失败: " + e.getMessage());
            e.printStackTrace();
        }

        if (examQuestions == null || examQuestions.isEmpty()) {
            String errorMsg = "考试题目数据格式错误";
            if (examResponse.getData() != null) {
                errorMsg += "，数据类型: " + examResponse.getData().getClass().getName();
                errorMsg += "，数据内容: " + examResponse.getData();
            }
            throw new IllegalArgumentException(errorMsg);
        }

        this.questions = examQuestions;
        this.userAnswers = new ArrayList<>();
        
        // 初始化用户答案
        for (int i = 0; i < questions.size(); i++) {
            userAnswers.add(null);
        }
        
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        setupFrame();
        startExamTimer();
        loadQuestion(0);
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeComponents() {
        // 题目显示 - 增大字体提高可读性
        questionLabel = new JLabel();
        questionLabel.setFont(new Font("微软雅黑", Font.PLAIN, 16));
        
        // 选项按钮 - 增大字体提高可读性
        optionA = new JRadioButton();
        optionA.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        optionB = new JRadioButton();
        optionB.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        optionC = new JRadioButton();
        optionC.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        optionD = new JRadioButton();
        optionD.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        
        optionGroup = new ButtonGroup();
        optionGroup.add(optionA);
        optionGroup.add(optionB);
        optionGroup.add(optionC);
        optionGroup.add(optionD);
        
        // 导航按钮 - 增大字体和按钮尺寸
        prevButton = new JButton("上一题");
        prevButton.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        nextButton = new JButton("下一题");
        nextButton.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        submitButton = new JButton("提交考试");
        submitButton.setFont(new Font("微软雅黑", Font.BOLD, 14));
        
        // 时间和进度标签 - 增大字体
        timeLabel = new JLabel();
        timeLabel.setFont(new Font("微软雅黑", Font.BOLD, 14));
        progressLabel = new JLabel();
        progressLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));

        // 学生信息标签
        studentInfoLabel = new JLabel("学号: 3240608005 姓名: 黄雁");
        studentInfoLabel.setFont(new Font("微软雅黑", Font.BOLD, 14));
        studentInfoLabel.setForeground(new Color(0, 102, 204)); // 蓝色显示
        
        // 题目列表
        questionListModel = new DefaultListModel<>();
        questionList = new JList<>(questionListModel);
        questionList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        // 初始化题目列表
        for (int i = 0; i < questions.size(); i++) {
            questionListModel.addElement("第" + (i + 1) + "题");
        }
        
        updateTimeDisplay();
        updateProgressDisplay();
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // 顶部信息面板
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 5, 10));

        JLabel titleLabel = new JLabel("在线考试");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 18)); // 增大标题字体
        topPanel.add(titleLabel, BorderLayout.WEST);

        // 中间显示学生信息
        topPanel.add(studentInfoLabel, BorderLayout.CENTER);

        JPanel infoPanel = new JPanel(new FlowLayout());
        infoPanel.add(timeLabel);
        infoPanel.add(new JLabel(" | "));
        infoPanel.add(progressLabel);
        topPanel.add(infoPanel, BorderLayout.EAST);
        
        add(topPanel, BorderLayout.NORTH);
        
        // 左侧题目列表
        JPanel leftPanel = new JPanel(new BorderLayout());
        leftPanel.setBorder(BorderFactory.createTitledBorder("题目导航"));
        leftPanel.setPreferredSize(new Dimension(150, 0));
        
        JScrollPane listScrollPane = new JScrollPane(questionList);
        leftPanel.add(listScrollPane, BorderLayout.CENTER);
        
        add(leftPanel, BorderLayout.WEST);
        
        // 中央答题区域
        JPanel centerPanel = new JPanel(new BorderLayout());
        centerPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // 题目内容面板
        JPanel questionPanel = new JPanel(new BorderLayout());
        questionPanel.setBorder(BorderFactory.createTitledBorder("题目内容"));
        
        JScrollPane questionScrollPane = new JScrollPane(questionLabel);
        questionScrollPane.setPreferredSize(new Dimension(0, 100));
        questionPanel.add(questionScrollPane, BorderLayout.CENTER);
        
        centerPanel.add(questionPanel, BorderLayout.NORTH);
        
        // 选项面板
        JPanel optionsPanel = new JPanel(new GridLayout(4, 1, 5, 5));
        optionsPanel.setBorder(BorderFactory.createTitledBorder("选择答案"));
        
        optionsPanel.add(optionA);
        optionsPanel.add(optionB);
        optionsPanel.add(optionC);
        optionsPanel.add(optionD);
        
        centerPanel.add(optionsPanel, BorderLayout.CENTER);
        
        add(centerPanel, BorderLayout.CENTER);
        
        // 底部按钮面板
        JPanel bottomPanel = new JPanel(new FlowLayout());
        bottomPanel.add(prevButton);
        bottomPanel.add(nextButton);
        bottomPanel.add(submitButton);
        
        add(bottomPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 选项选择事件
        ActionListener optionListener = new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                saveCurrentAnswer();
                updateQuestionListDisplay();
            }
        };
        
        optionA.addActionListener(optionListener);
        optionB.addActionListener(optionListener);
        optionC.addActionListener(optionListener);
        optionD.addActionListener(optionListener);
        
        // 导航按钮事件
        prevButton.addActionListener(e -> {
            if (currentQuestionIndex > 0) {
                saveCurrentAnswer();
                loadQuestion(currentQuestionIndex - 1);
            }
        });
        
        nextButton.addActionListener(e -> {
            if (currentQuestionIndex < questions.size() - 1) {
                saveCurrentAnswer();
                loadQuestion(currentQuestionIndex + 1);
            }
        });
        
        submitButton.addActionListener(e -> submitExam());
        
        // 题目列表选择事件
        questionList.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                int selectedIndex = questionList.getSelectedIndex();
                if (selectedIndex >= 0 && selectedIndex != currentQuestionIndex) {
                    saveCurrentAnswer();
                    loadQuestion(selectedIndex);
                }
            }
        });
        
        // 窗口关闭事件
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        addWindowListener(new java.awt.event.WindowAdapter() {
            @Override
            public void windowClosing(java.awt.event.WindowEvent e) {
                int option = JOptionPane.showConfirmDialog(ExamFrame.this,
                    "考试尚未完成，确定要退出吗？\n退出后将自动提交已答题目。",
                    "确认退出", JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);
                
                if (option == JOptionPane.YES_OPTION) {
                    submitExam();
                }
            }
        });
    }
    
    /**
     * 设置窗口属性
     */
    private void setupFrame() {
        setTitle("在线考试 - 学号: 3240608005 姓名: 黄雁");
        setSize(800, 600); // 减小窗口尺寸，不再最大化
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);

        // 添加窗口关闭事件处理
        addWindowListener(new java.awt.event.WindowAdapter() {
            @Override
            public void windowClosing(java.awt.event.WindowEvent e) {
                ExamFrame.this.handleExamExit();
            }
        });
    }
    
    /**
     * 启动考试计时器
     */
    private void startExamTimer() {
        examTimer = new Timer(1000, new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                timeRemaining--;
                updateTimeDisplay();
                
                if (timeRemaining <= 0) {
                    examTimer.stop();
                    JOptionPane.showMessageDialog(ExamFrame.this,
                        "考试时间到！系统将自动提交答案。",
                        "时间到", JOptionPane.WARNING_MESSAGE);
                    submitExam();
                }
            }
        });
        examTimer.start();
    }
    
    /**
     * 加载指定题目
     */
    private void loadQuestion(int index) {
        if (index < 0 || index >= questions.size()) {
            return;
        }
        
        currentQuestionIndex = index;
        Question question = questions.get(index);
        
        // 显示题目内容
        questionLabel.setText("<html><body style='width: 500px'>" +
            "第" + (index + 1) + "题：" + question.getQuestionContent() +
            "</body></html>");
        
        // 显示选项
        optionA.setText("A. " + question.getOptionA());
        optionB.setText("B. " + question.getOptionB());
        optionC.setText("C. " + question.getOptionC());
        optionD.setText("D. " + question.getOptionD());
        
        // 恢复用户答案
        Question.Answer userAnswer = userAnswers.get(index);
        optionGroup.clearSelection();
        
        if (userAnswer != null) {
            switch (userAnswer) {
                case A: optionA.setSelected(true); break;
                case B: optionB.setSelected(true); break;
                case C: optionC.setSelected(true); break;
                case D: optionD.setSelected(true); break;
            }
        }
        
        // 更新按钮状态
        prevButton.setEnabled(index > 0);
        nextButton.setEnabled(index < questions.size() - 1);
        
        // 更新题目列表选择
        questionList.setSelectedIndex(index);
        
        updateProgressDisplay();
    }
    
    /**
     * 保存当前答案
     */
    private void saveCurrentAnswer() {
        Question.Answer answer = null;
        
        if (optionA.isSelected()) answer = Question.Answer.A;
        else if (optionB.isSelected()) answer = Question.Answer.B;
        else if (optionC.isSelected()) answer = Question.Answer.C;
        else if (optionD.isSelected()) answer = Question.Answer.D;
        
        userAnswers.set(currentQuestionIndex, answer);
    }
    
    /**
     * 提交考试
     */
    private void submitExam() {
        saveCurrentAnswer();
        
        // 检查未答题目并显示详细信息
        int unansweredCount = 0;
        int answeredCount = 0;
        for (Question.Answer answer : userAnswers) {
            if (answer == null) {
                unansweredCount++;
            } else {
                answeredCount++;
            }
        }

        if (unansweredCount > 0) {
            String message = String.format(
                "考试提交确认：\n\n" +
                "总题数：%d 道\n" +
                "已答题数：%d 道\n" +
                "未答题数：%d 道\n\n" +
                "确定要提交考试吗？\n" +
                "（未答题目将按错误计算）",
                questions.size(), answeredCount, unansweredCount
            );

            int option = JOptionPane.showConfirmDialog(this,
                message, "确认提交考试",
                JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);

            if (option != JOptionPane.YES_OPTION) {
                return;
            }
        } else {
            // 全部答完的确认提示
            int option = JOptionPane.showConfirmDialog(this,
                String.format("已完成全部 %d 道题目，确定要提交考试吗？", questions.size()),
                "确认提交考试", JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);

            if (option != JOptionPane.YES_OPTION) {
                return;
            }
        }
        
        // 停止计时器
        if (examTimer != null) {
            examTimer.stop();
        }
        
        // 计算考试结果
        int timeUsed = examDuration - timeRemaining;
        List<ExamDetail> examDetails = new ArrayList<>();
        
        for (int i = 0; i < questions.size(); i++) {
            ExamDetail detail = examController.createExamDetail(
                null, questions.get(i), userAnswers.get(i));
            examDetails.add(detail);
        }
        
        ExamRecord examRecord = examController.calculateExamScore(
            examDetails, timeUsed, examDuration);
        
        // 提交到服务器
        SwingWorker<Message, Void> worker = new SwingWorker<Message, Void>() {
            @Override
            protected Message doInBackground() throws Exception {
                return examController.submitExam(examRecord);
            }
            
            @Override
            protected void done() {
                try {
                    Message response = get();
                    if (response != null && response.isSuccess()) {
                        showExamResult(examRecord);
                    } else {
                        String errorMsg = "提交考试失败";
                        if (response != null && response.getMessage() != null && !response.getMessage().trim().isEmpty()) {
                            errorMsg += ": " + response.getMessage();
                        } else {
                            errorMsg += ": 服务器响应异常";
                        }

                        // 提供选项：重试或退出
                        String[] options = {"重试提交", "退出考试"};
                        int choice = JOptionPane.showOptionDialog(ExamFrame.this,
                            errorMsg + "\n\n请选择操作：",
                            "提交失败", JOptionPane.YES_NO_OPTION, JOptionPane.ERROR_MESSAGE,
                            null, options, options[0]);

                        if (choice == 1) { // 选择退出
                            returnToMainInterface();
                        }
                        // 选择重试则不做任何操作，用户可以再次点击提交
                    }
                } catch (Exception e) {
                    String errorMsg = "提交考试异常";
                    if (e.getMessage() != null && !e.getMessage().trim().isEmpty()) {
                        errorMsg += ": " + e.getMessage();
                    } else {
                        errorMsg += ": 网络连接异常";
                    }

                    // 提供选项：重试或退出
                    String[] options = {"重试提交", "退出考试"};
                    int choice = JOptionPane.showOptionDialog(ExamFrame.this,
                        errorMsg + "\n\n请选择操作：",
                        "提交异常", JOptionPane.YES_NO_OPTION, JOptionPane.ERROR_MESSAGE,
                        null, options, options[0]);

                    if (choice == 1) { // 选择退出
                        returnToMainInterface();
                    }
                }
            }
        };
        worker.execute();
    }
    
    /**
     * 显示考试结果
     */
    private void showExamResult(ExamRecord examRecord) {
        String result = String.format(
            "考试完成！\n\n" +
            "总题数：%d\n" +
            "正确答案数：%d\n" +
            "考试成绩：%.1f分\n" +
            "用时：%s\n" +
            "成绩等级：%s",
            examRecord.getTotalQuestions(),
            examRecord.getCorrectAnswers(),
            examRecord.getScore().doubleValue(),
            examController.formatTime(examRecord.getTimeUsed()),
            examRecord.getGradeLevel()
        );
        
        JOptionPane.showMessageDialog(this, result, "考试结果", JOptionPane.INFORMATION_MESSAGE);
        
        // 返回学生主界面
        SwingUtilities.invokeLater(() -> {
            StudentFrame studentFrame = new StudentFrame(client);
            studentFrame.setVisible(true);
            dispose();
        });
    }
    
    /**
     * 更新时间显示
     */
    private void updateTimeDisplay() {
        timeLabel.setText("剩余时间：" + examController.formatTime(timeRemaining));
        
        // 时间不足时变红
        if (timeRemaining <= 300) { // 5分钟
            timeLabel.setForeground(Color.RED);
        } else if (timeRemaining <= 600) { // 10分钟
            timeLabel.setForeground(Color.ORANGE);
        } else {
            timeLabel.setForeground(Color.BLACK);
        }
    }
    
    /**
     * 更新进度显示
     */
    private void updateProgressDisplay() {
        int answered = 0;
        for (Question.Answer answer : userAnswers) {
            if (answer != null) {
                answered++;
            }
        }
        
        progressLabel.setText(String.format("进度：%d/%d (已答%d题)",
            currentQuestionIndex + 1, questions.size(), answered));
    }

    /**
     * 处理考试退出
     */
    private void handleExamExit() {
        // 停止计时器
        if (examTimer != null) {
            examTimer.stop();
        }

        int option = JOptionPane.showConfirmDialog(this,
            "确定要退出考试吗？\n退出后将无法继续答题。",
            "确认退出", JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);

        if (option == JOptionPane.YES_OPTION) {
            returnToMainInterface();
        } else {
            // 重新启动计时器
            if (examTimer != null && timeRemaining > 0) {
                examTimer.start();
            }
        }
    }

    /**
     * 返回主界面
     */
    private void returnToMainInterface() {
        SwingUtilities.invokeLater(() -> {
            // 根据用户类型返回相应界面
            if (client.getCurrentUser().isStudent()) {
                StudentFrame studentFrame = new StudentFrame(client);
                studentFrame.setVisible(true);
            } else {
                AdminFrame adminFrame = new AdminFrame(client);
                adminFrame.setVisible(true);
            }
            dispose();
        });
    }
    
    /**
     * 更新题目列表显示
     */
    private void updateQuestionListDisplay() {
        for (int i = 0; i < questions.size(); i++) {
            String text = "第" + (i + 1) + "题";
            if (userAnswers.get(i) != null) {
                text += " ✓";
            }
            questionListModel.setElementAt(text, i);
        }
    }
}
