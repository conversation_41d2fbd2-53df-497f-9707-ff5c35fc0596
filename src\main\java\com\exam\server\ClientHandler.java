package com.exam.server;

import com.exam.model.*;
import com.exam.util.NetworkUtil;

import java.io.IOException;
import java.net.Socket;
import java.util.List;

/**
 * 客户端处理器
 * 实现MVC模式中的Controller层
 * 处理单个客户端的所有请求
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ClientHandler implements Runnable {
    
    private Socket clientSocket;
    private DatabaseManager dbManager;
    private String sessionId;
    private User currentUser;
    private boolean isRunning;
    
    /**
     * 构造函数
     * @param clientSocket 客户端Socket连接
     * @param dbManager 数据库管理器
     */
    public ClientHandler(Socket clientSocket, DatabaseManager dbManager) {
        this.clientSocket = clientSocket;
        this.dbManager = dbManager;
        this.sessionId = NetworkUtil.generateSessionId();
        this.isRunning = true;
        
        System.out.println("新客户端连接: " + clientSocket.getRemoteSocketAddress() + 
                          ", 会话ID: " + sessionId);
    }
    
    @Override
    public void run() {
        try {
            // 处理客户端请求
            while (isRunning && NetworkUtil.isSocketValid(clientSocket)) {
                try {
                    // 接收客户端消息
                    Message request = NetworkUtil.receiveMessage(clientSocket);

                    if (request == null) {
                        break;
                    }

                    // 设置会话信息
                    request.setSessionId(sessionId);

                    // 处理请求并发送响应
                    Message response = handleRequest(request);
                    if (response != null) {
                        response.setSessionId(sessionId);
                        NetworkUtil.sendMessage(response, clientSocket);
                    }
                    
                } catch (IOException e) {
                    System.err.println("处理客户端请求失败: " + e.getMessage());
                    break;
                }
            }
            
        } catch (Exception e) {
            System.err.println("客户端处理器异常: " + e.getMessage());
        } finally {
            cleanup();
        }
    }
    
    /**
     * 处理客户端请求
     * @param request 请求消息
     * @return 响应消息
     */
    private Message handleRequest(Message request) {
        try {
            switch (request.getType()) {
                case LOGIN_REQUEST:
                    return handleLogin(request);
                    
                case REGISTER_REQUEST:
                    return handleRegister(request);
                    
                case LOGOUT_REQUEST:
                    return handleLogout(request);
                    
                case QUESTION_LIST_REQUEST:
                    return handleQuestionList(request);

                case QUESTION_COUNT_REQUEST:
                    return handleQuestionCount(request);

                case QUESTION_ADD_REQUEST:
                    return handleQuestionAdd(request);
                    
                case QUESTION_UPDATE_REQUEST:
                    return handleQuestionUpdate(request);
                    
                case QUESTION_DELETE_REQUEST:
                    return handleQuestionDelete(request);
                    
                case EXAM_START_REQUEST:
                    return handleExamStart(request);
                    
                case EXAM_SUBMIT_REQUEST:
                    return handleExamSubmit(request);
                    
                case SCORE_LIST_REQUEST:
                    return handleScoreList(request);
                    
                case HEARTBEAT:
                    return handleHeartbeat(request);
                    
                default:
                    return NetworkUtil.createErrorResponse(request, "不支持的请求类型");
            }
            
        } catch (Exception e) {
            System.err.println("处理请求异常: " + e.getMessage());
            return NetworkUtil.createErrorResponse(request, "服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 处理用户登录
     * @param request 登录请求
     * @return 登录响应
     */
    private Message handleLogin(Message request) {
        String username = request.getStringParam("username");
        String password = request.getStringParam("password");
        
        if (username == null || password == null) {
            return Message.createErrorResponse(request.getType(), "用户名或密码不能为空");
        }
        
        User user = dbManager.authenticateUser(username, password);
        
        if (user != null) {
            this.currentUser = user;
            System.out.println("用户登录成功: " + user.getUsername() + " (" + user.getRealName() + ")");
            
            Message response = Message.createSuccessResponse(request.getType(), user);
            response.setUserId(user.getUserId());
            return response;
        } else {
            return Message.createErrorResponse(request.getType(), "用户名或密码错误");
        }
    }
    
    /**
     * 处理用户注册
     * @param request 注册请求
     * @return 注册响应
     */
    private Message handleRegister(Message request) {
        try {
            User user = NetworkUtil.fromJson(NetworkUtil.toJson(request.getData()), User.class);
            
            if (user == null || !user.isValid()) {
                return Message.createErrorResponse(request.getType(), "用户信息不完整");
            }
            
            if (dbManager.registerUser(user)) {
                System.out.println("用户注册成功: " + user.getUsername());
                return Message.createSuccessResponse(request.getType(), "注册成功");
            } else {
                return Message.createErrorResponse(request.getType(), "注册失败，用户名可能已存在");
            }
            
        } catch (Exception e) {
            return Message.createErrorResponse(request.getType(), "注册失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理用户登出
     * @param request 登出请求
     * @return 登出响应
     */
    private Message handleLogout(Message request) {
        if (currentUser != null) {
            System.out.println("用户登出: " + currentUser.getUsername());
            currentUser = null;
        }
        
        return Message.createSuccessResponse(request.getType(), "登出成功");
    }
    
    /**
     * 处理题目列表请求
     * @param request 请求消息
     * @return 响应消息
     */
    private Message handleQuestionList(Message request) {
        if (!isUserLoggedIn()) {
            return Message.createErrorResponse(request.getType(), "请先登录");
        }
        
        List<Question> questions = dbManager.getAllQuestions();
        return Message.createSuccessResponse(request.getType(), questions);
    }
    
    /**
     * 处理题目数量请求
     * @param request 请求消息
     * @return 响应消息
     */
    private Message handleQuestionCount(Message request) {
        try {
            int availableCount = dbManager.getAvailableQuestionCount();
            return Message.createSuccessResponse(request.getType(), availableCount);
        } catch (Exception e) {
            System.err.println("获取题目数量失败: " + e.getMessage());
            e.printStackTrace();
            return Message.createErrorResponse(request.getType(), "获取题目数量失败: " + e.getMessage());
        }
    }

    /**
     * 处理添加题目请求
     * @param request 请求消息
     * @return 响应消息
     */
    private Message handleQuestionAdd(Message request) {
        if (!isAdminUser()) {
            return Message.createErrorResponse(request.getType(), "权限不足，只有管理员可以添加题目");
        }
        
        try {
            Question question = NetworkUtil.fromJson(NetworkUtil.toJson(request.getData()), Question.class);
            
            if (question == null || !question.isValid()) {
                return Message.createErrorResponse(request.getType(), "题目信息不完整");
            }
            
            question.setCreatedBy(currentUser.getUserId());
            
            if (dbManager.addQuestion(question)) {
                return Message.createSuccessResponse(request.getType(), "题目添加成功");
            } else {
                return Message.createErrorResponse(request.getType(), "题目添加失败");
            }
            
        } catch (Exception e) {
            return Message.createErrorResponse(request.getType(), "添加题目失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理更新题目请求
     * @param request 请求消息
     * @return 响应消息
     */
    private Message handleQuestionUpdate(Message request) {
        if (!isAdminUser()) {
            return Message.createErrorResponse(request.getType(), "权限不足，只有管理员可以修改题目");
        }
        
        try {
            Question question = NetworkUtil.fromJson(NetworkUtil.toJson(request.getData()), Question.class);
            
            if (question == null || !question.isValid() || question.getQuestionId() == null) {
                return Message.createErrorResponse(request.getType(), "题目信息不完整");
            }
            
            if (dbManager.updateQuestion(question)) {
                return Message.createSuccessResponse(request.getType(), "题目更新成功");
            } else {
                return Message.createErrorResponse(request.getType(), "题目更新失败");
            }
            
        } catch (Exception e) {
            return Message.createErrorResponse(request.getType(), "更新题目失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理删除题目请求
     * @param request 请求消息
     * @return 响应消息
     */
    private Message handleQuestionDelete(Message request) {
        if (!isAdminUser()) {
            return Message.createErrorResponse(request.getType(), "权限不足，只有管理员可以删除题目");
        }

        Object questionIdParam = request.getParam("questionId");
        Integer questionId = null;

        if (questionIdParam == null) {
            System.err.println("删除题目失败：题目ID参数为空，请求参数: " + request.getParams());
            return Message.createErrorResponse(request.getType(), "题目ID不能为空");
        }

        try {
            if (questionIdParam instanceof Number) {
                questionId = ((Number) questionIdParam).intValue();
            } else if (questionIdParam instanceof String) {
                questionId = Integer.parseInt((String) questionIdParam);
            } else {
                System.err.println("题目ID参数类型错误: " + questionIdParam.getClass().getName());
                return Message.createErrorResponse(request.getType(), "题目ID参数类型错误");
            }
        } catch (NumberFormatException e) {
            System.err.println("题目ID格式错误: " + questionIdParam);
            return Message.createErrorResponse(request.getType(), "题目ID格式错误");
        }

        if (questionId <= 0) {
            return Message.createErrorResponse(request.getType(), "题目ID无效");
        }

        try {
            boolean result = dbManager.deleteQuestion(questionId);
            if (result) {
                return Message.createSuccessResponse(request.getType(), "题目删除成功");
            } else {
                System.err.println("题目删除失败: ID=" + questionId + "，可能题目不存在或已被删除");
                return Message.createErrorResponse(request.getType(), "题目删除失败，可能题目不存在或已被删除");
            }
        } catch (Exception e) {
            System.err.println("删除题目异常: " + e.getMessage());
            e.printStackTrace();
            return Message.createErrorResponse(request.getType(), "删除题目异常: " + e.getMessage());
        }
    }
    
    /**
     * 处理开始考试请求
     * @param request 请求消息
     * @return 响应消息
     */
    private Message handleExamStart(Message request) {
        if (!isUserLoggedIn()) {
            return Message.createErrorResponse(request.getType(), "请先登录");
        }
        
        if (!currentUser.isStudent()) {
            return Message.createErrorResponse(request.getType(), "只有学生可以参加考试");
        }
        
        Integer questionCount = request.getIntParam("questionCount");
        if (questionCount == null || questionCount <= 0) {
            questionCount = 10; // 默认10道题
        }

        // 检查题库中可用题目总数
        int availableQuestionCount = dbManager.getAvailableQuestionCount();
        if (availableQuestionCount == 0) {
            return Message.createErrorResponse(request.getType(), "题库中没有可用题目");
        }

        if (questionCount > availableQuestionCount) {
            return Message.createErrorResponse(request.getType(),
                String.format("题目数量超出限制，题库中只有 %d 道可用题目", availableQuestionCount));
        }

        List<Question> examQuestions = dbManager.getRandomQuestions(questionCount);

        if (examQuestions.isEmpty()) {
            System.err.println("获取考试题目失败：题目列表为空");
            return Message.createErrorResponse(request.getType(), "获取考试题目失败");
        }

        if (examQuestions.size() < questionCount) {
            System.err.println("题目数量不足：请求" + questionCount + "道，实际获取" + examQuestions.size() + "道");
            return Message.createErrorResponse(request.getType(),
                String.format("题目数量不足，只获取到 %d 道题目", examQuestions.size()));
        }

        return Message.createSuccessResponse(request.getType(), examQuestions);
    }
    
    /**
     * 处理提交考试请求
     * @param request 请求消息
     * @return 响应消息
     */
    private Message handleExamSubmit(Message request) {
        if (!isUserLoggedIn()) {
            return Message.createErrorResponse(request.getType(), "请先登录");
        }
        
        if (!currentUser.isStudent()) {
            return Message.createErrorResponse(request.getType(), "只有学生可以提交考试");
        }
        
        try {
            ExamRecord examRecord = NetworkUtil.fromJson(NetworkUtil.toJson(request.getData()), ExamRecord.class);

            if (examRecord == null) {
                return Message.createErrorResponse(request.getType(), "考试记录不能为空");
            }

            // 验证考试记录的完整性
            if (examRecord.getTotalQuestions() == null || examRecord.getTotalQuestions() <= 0) {
                return Message.createErrorResponse(request.getType(), "考试记录数据不完整：题目数量无效");
            }

            if (examRecord.getScore() == null) {
                return Message.createErrorResponse(request.getType(), "考试记录数据不完整：成绩信息缺失");
            }

            examRecord.setUserId(currentUser.getUserId());
            examRecord.setUserName(currentUser.getRealName());

            Integer recordId = dbManager.saveExamRecord(examRecord);

            if (recordId != null && recordId > 0) {
                examRecord.setRecordId(recordId);
                System.out.println("考试提交成功: 用户=" + currentUser.getUsername() +
                                 ", 成绩=" + examRecord.getScore());
                return Message.createSuccessResponse(request.getType(), examRecord);
            } else {
                return Message.createErrorResponse(request.getType(), "考试记录保存失败：数据库操作异常");
            }

        } catch (Exception e) {
            System.err.println("提交考试异常: " + e.getMessage());
            e.printStackTrace();
            String errorMsg = "提交考试失败";
            if (e.getMessage() != null && !e.getMessage().trim().isEmpty()) {
                errorMsg += ": " + e.getMessage();
            } else {
                errorMsg += ": 服务器内部错误";
            }
            return Message.createErrorResponse(request.getType(), errorMsg);
        }
    }
    
    /**
     * 处理成绩列表请求
     * @param request 请求消息
     * @return 响应消息
     */
    private Message handleScoreList(Message request) {
        if (!isUserLoggedIn()) {
            return Message.createErrorResponse(request.getType(), "请先登录");
        }

        System.out.println("ClientHandler: 处理成绩列表请求，用户: " + currentUser.getUsername() + "，角色: " + currentUser.getUserType());

        List<ExamRecord> records;

        if (currentUser.isAdmin()) {
            // 管理员可以查看所有成绩
            records = dbManager.getAllExamRecords();
            System.out.println("ClientHandler: 管理员获取所有成绩记录，共 " + records.size() + " 条");
        } else {
            // 学生只能查看自己的成绩
            records = dbManager.getUserExamRecords(currentUser.getUserId());
            System.out.println("ClientHandler: 学生获取个人成绩记录，共 " + records.size() + " 条");
        }

        Message response = Message.createSuccessResponse(request.getType(), records);
        System.out.println("ClientHandler: 返回成绩列表响应，类型: " + response.getType() + "，数据类型: " +
                         (response.getData() != null ? response.getData().getClass().getName() : "null"));
        return response;
    }
    
    /**
     * 处理心跳包
     * @param request 心跳请求
     * @return 心跳响应
     */
    private Message handleHeartbeat(Message request) {
        Message response = new Message(Message.MessageType.HEARTBEAT);
        response.setStatus(Message.MessageStatus.SUCCESS);
        response.setMessage("心跳响应");
        return response;
    }
    
    /**
     * 检查用户是否已登录
     * @return true如果已登录，否则false
     */
    private boolean isUserLoggedIn() {
        return currentUser != null;
    }
    
    /**
     * 检查当前用户是否为管理员
     * @return true如果是管理员，否则false
     */
    private boolean isAdminUser() {
        return isUserLoggedIn() && currentUser.isAdmin();
    }
    
    /**
     * 停止客户端处理器
     */
    public void stop() {
        isRunning = false;
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        try {
            if (currentUser != null) {
                System.out.println("客户端断开连接: " + currentUser.getUsername());
            } else {
                System.out.println("客户端断开连接: " + sessionId);
            }
            
            NetworkUtil.closeSocket(clientSocket);
            
        } catch (Exception e) {
            System.err.println("清理客户端资源失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取会话ID
     * @return 会话ID
     */
    public String getSessionId() {
        return sessionId;
    }
    
    /**
     * 获取当前用户
     * @return 当前用户对象
     */
    public User getCurrentUser() {
        return currentUser;
    }
}
