package com.exam.controller;

import com.exam.client.ExamClient;
import com.exam.model.Message;
import com.exam.model.Question;
import com.exam.util.NetworkUtil;

import java.io.IOException;
import java.util.List;

/**
 * 题目控制器
 * 实现MVC模式中的Controller层
 * 处理题目相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class QuestionController {
    
    private ExamClient client;
    
    /**
     * 构造函数
     * @param client 客户端对象
     */
    public QuestionController(ExamClient client) {
        this.client = client;
    }
    
    /**
     * 获取所有题目列表
     * @return 题目列表响应消息
     */
    public Message getAllQuestions() {
        try {
            Message request = new Message(Message.MessageType.QUESTION_LIST_REQUEST);
            Message response = client.sendMessage(request);
            
            if (response.isSuccess()) {
                // 将响应数据转换为题目列表
                @SuppressWarnings("unchecked")
                List<Question> questions = (List<Question>) response.getData();
                response.setData(questions);
            }
            
            return response;
            
        } catch (IOException e) {
            return createErrorMessage("获取题目列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 添加新题目
     * @param question 题目对象
     * @return 添加结果消息
     */
    public Message addQuestion(Question question) {
        // 验证题目信息
        String validationError = validateQuestion(question);
        if (validationError != null) {
            return createErrorMessage(validationError);
        }
        
        try {
            Message request = new Message(Message.MessageType.QUESTION_ADD_REQUEST, question);
            return client.sendMessage(request);
            
        } catch (IOException e) {
            return createErrorMessage("添加题目失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新题目
     * @param question 题目对象
     * @return 更新结果消息
     */
    public Message updateQuestion(Question question) {
        // 验证题目信息
        String validationError = validateQuestion(question);
        if (validationError != null) {
            return createErrorMessage(validationError);
        }
        
        if (question.getQuestionId() == null) {
            return createErrorMessage("题目ID不能为空");
        }
        
        try {
            Message request = new Message(Message.MessageType.QUESTION_UPDATE_REQUEST, question);
            return client.sendMessage(request);
            
        } catch (IOException e) {
            return createErrorMessage("更新题目失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除题目
     * @param questionId 题目ID
     * @return 删除结果消息
     */
    public Message deleteQuestion(Integer questionId) {
        if (questionId == null) {
            return createErrorMessage("题目ID不能为空");
        }
        
        try {
            Message request = new Message(Message.MessageType.QUESTION_DELETE_REQUEST);
            request.addParam("questionId", questionId);
            return client.sendMessage(request);
            
        } catch (IOException e) {
            return createErrorMessage("删除题目失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取考试题目
     * @param questionCount 题目数量
     * @return 考试题目响应消息
     */
    public Message getExamQuestions(int questionCount) {
        if (questionCount <= 0) {
            return createErrorMessage("题目数量必须大于0");
        }
        
        if (questionCount > 100) {
            return createErrorMessage("题目数量不能超过100");
        }
        
        try {
            Message request = new Message(Message.MessageType.EXAM_START_REQUEST);
            request.addParam("questionCount", questionCount);
            Message response = client.sendMessage(request);
            
            if (response.isSuccess()) {
                // 将响应数据转换为题目列表
                @SuppressWarnings("unchecked")
                List<Question> questions = (List<Question>) response.getData();
                response.setData(questions);
            }
            
            return response;
            
        } catch (IOException e) {
            return createErrorMessage("获取考试题目失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证题目信息
     * @param question 题目对象
     * @return 验证错误信息，如果验证通过返回null
     */
    private String validateQuestion(Question question) {
        if (question == null) {
            return "题目信息不能为空";
        }
        
        // 验证题目内容
        if (question.getQuestionContent() == null || question.getQuestionContent().trim().isEmpty()) {
            return "题目内容不能为空";
        }
        
        if (question.getQuestionContent().length() > 1000) {
            return "题目内容不能超过1000个字符";
        }
        
        // 验证选项A
        if (question.getOptionA() == null || question.getOptionA().trim().isEmpty()) {
            return "选项A不能为空";
        }
        
        if (question.getOptionA().length() > 500) {
            return "选项A不能超过500个字符";
        }
        
        // 验证选项B
        if (question.getOptionB() == null || question.getOptionB().trim().isEmpty()) {
            return "选项B不能为空";
        }
        
        if (question.getOptionB().length() > 500) {
            return "选项B不能超过500个字符";
        }
        
        // 验证选项C
        if (question.getOptionC() == null || question.getOptionC().trim().isEmpty()) {
            return "选项C不能为空";
        }
        
        if (question.getOptionC().length() > 500) {
            return "选项C不能超过500个字符";
        }
        
        // 验证选项D
        if (question.getOptionD() == null || question.getOptionD().trim().isEmpty()) {
            return "选项D不能为空";
        }
        
        if (question.getOptionD().length() > 500) {
            return "选项D不能超过500个字符";
        }
        
        // 验证正确答案
        if (question.getCorrectAnswer() == null) {
            return "正确答案不能为空";
        }
        
        // 验证难度等级
        if (question.getDifficulty() == null) {
            return "难度等级不能为空";
        }
        
        // 验证科目
        if (question.getSubject() == null || question.getSubject().trim().isEmpty()) {
            return "科目不能为空";
        }
        
        if (question.getSubject().length() > 100) {
            return "科目名称不能超过100个字符";
        }
        
        return null; // 验证通过
    }
    
    /**
     * 创建题目对象
     * @param content 题目内容
     * @param optionA 选项A
     * @param optionB 选项B
     * @param optionC 选项C
     * @param optionD 选项D
     * @param correctAnswer 正确答案
     * @param difficulty 难度等级
     * @param subject 科目
     * @return 题目对象
     */
    public Question createQuestion(String content, String optionA, String optionB, 
                                 String optionC, String optionD, Question.Answer correctAnswer,
                                 Question.Difficulty difficulty, String subject) {
        Question question = new Question();
        question.setQuestionContent(content);
        question.setOptionA(optionA);
        question.setOptionB(optionB);
        question.setOptionC(optionC);
        question.setOptionD(optionD);
        question.setCorrectAnswer(correctAnswer);
        question.setDifficulty(difficulty);
        question.setSubject(subject);
        
        return question;
    }
    
    /**
     * 检查题目是否有效
     * @param question 题目对象
     * @return true如果有效，否则false
     */
    public boolean isQuestionValid(Question question) {
        return validateQuestion(question) == null;
    }
    
    /**
     * 获取题目统计信息
     * @param questions 题目列表
     * @return 统计信息字符串
     */
    public String getQuestionStatistics(List<Question> questions) {
        if (questions == null || questions.isEmpty()) {
            return "暂无题目";
        }
        
        int total = questions.size();
        int easy = 0, medium = 0, hard = 0;
        
        for (Question question : questions) {
            switch (question.getDifficulty()) {
                case EASY:
                    easy++;
                    break;
                case MEDIUM:
                    medium++;
                    break;
                case HARD:
                    hard++;
                    break;
            }
        }
        
        return String.format("总题数: %d, 简单: %d, 中等: %d, 困难: %d", 
                           total, easy, medium, hard);
    }
    
    /**
     * 创建错误消息
     * @param errorMessage 错误信息
     * @return 错误消息对象
     */
    private Message createErrorMessage(String errorMessage) {
        Message message = new Message();
        message.setStatus(Message.MessageStatus.ERROR);
        message.setMessage(errorMessage);
        return message;
    }
    
    /**
     * 创建成功消息
     * @param successMessage 成功信息
     * @return 成功消息对象
     */
    private Message createSuccessMessage(String successMessage) {
        Message message = new Message();
        message.setStatus(Message.MessageStatus.SUCCESS);
        message.setMessage(successMessage);
        return message;
    }
}
