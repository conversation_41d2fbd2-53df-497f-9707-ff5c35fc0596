package com.exam.server;

import com.exam.model.*;
import com.exam.util.DatabaseUtil;

import java.math.BigDecimal;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据库管理器
 * 实现MVC模式中的Model层数据访问
 * 提供所有数据库操作的封装
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class DatabaseManager {
    
    /**
     * 用户登录验证
     * @param username 用户名
     * @param password 密码
     * @return 用户对象，如果验证失败返回null
     */
    public User authenticateUser(String username, String password) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            String sql = "SELECT * FROM users WHERE username = ? AND password = ? AND status = 'ACTIVE'";
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, username);
            pstmt.setString(2, password);
            
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return mapResultSetToUser(rs);
            }
            
        } catch (SQLException e) {
            System.err.println("用户认证失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeAll(conn, pstmt, rs);
        }
        
        return null;
    }
    
    /**
     * 用户注册
     * @param user 用户对象
     * @return true如果注册成功，否则false
     */
    public boolean registerUser(User user) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            
            // 检查用户名是否已存在
            if (isUsernameExists(user.getUsername())) {
                System.err.println("用户名已存在: " + user.getUsername());
                return false;
            }
            
            String sql = "INSERT INTO users (username, password, real_name, user_type, email, phone) VALUES (?, ?, ?, ?, ?, ?)";
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, user.getUsername());
            pstmt.setString(2, user.getPassword());
            pstmt.setString(3, user.getRealName());
            pstmt.setString(4, user.getUserType().getCode());
            pstmt.setString(5, user.getEmail());
            pstmt.setString(6, user.getPhone());
            
            int result = pstmt.executeUpdate();
            DatabaseUtil.commit(conn);
            
            return result > 0;
            
        } catch (SQLException e) {
            System.err.println("用户注册失败: " + e.getMessage());
            DatabaseUtil.rollback(conn);
            return false;
        } finally {
            DatabaseUtil.closeAll(conn, pstmt, null);
        }
    }
    
    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return true如果存在，否则false
     */
    public boolean isUsernameExists(String username) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            String sql = "SELECT COUNT(*) FROM users WHERE username = ?";
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, username);
            
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
            
        } catch (SQLException e) {
            System.err.println("检查用户名失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeAll(conn, pstmt, rs);
        }
        
        return false;
    }
    
    /**
     * 获取所有题目
     * @return 题目列表
     */
    public List<Question> getAllQuestions() {
        List<Question> questions = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DatabaseUtil.getConnection();
            String sql = "SELECT * FROM questions WHERE status = 'ACTIVE' ORDER BY question_id";
            pstmt = conn.prepareStatement(sql);

            rs = pstmt.executeQuery();

            while (rs.next()) {
                try {
                    questions.add(mapResultSetToQuestion(rs));
                } catch (Exception e) {
                    System.err.println("映射题目数据失败: " + e.getMessage());
                    e.printStackTrace();
                }
            }

        } catch (SQLException e) {
            System.err.println("获取题目列表失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeAll(conn, pstmt, rs);
        }

        return questions;
    }
    
    /**
     * 获取可用题目总数
     * @return 可用题目数量
     */
    public int getAvailableQuestionCount() {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DatabaseUtil.getConnection();
            String sql = "SELECT COUNT(*) FROM questions WHERE status = 'ACTIVE'";
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();

            if (rs.next()) {
                return rs.getInt(1);
            }

        } catch (SQLException e) {
            System.err.println("获取可用题目数量失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeAll(conn, pstmt, rs);
        }

        return 0;
    }

    /**
     * 随机获取指定数量的题目
     * @param count 题目数量
     * @return 题目列表
     */
    public List<Question> getRandomQuestions(int count) {
        List<Question> questions = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            String sql = "SELECT * FROM questions WHERE status = 'ACTIVE' ORDER BY RAND() LIMIT ?";
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, count);
            
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                questions.add(mapResultSetToQuestion(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("获取随机题目失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeAll(conn, pstmt, rs);
        }
        
        return questions;
    }
    
    /**
     * 添加题目
     * @param question 题目对象
     * @return true如果添加成功，否则false
     */
    public boolean addQuestion(Question question) {
        Connection conn = null;
        PreparedStatement pstmt = null;

        try {
            conn = DatabaseUtil.getConnection();
            String sql = "INSERT INTO questions (question_content, option_a, option_b, option_c, option_d, correct_answer, difficulty, subject, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, question.getQuestionContent());
            pstmt.setString(2, question.getOptionA());
            pstmt.setString(3, question.getOptionB());
            pstmt.setString(4, question.getOptionC());
            pstmt.setString(5, question.getOptionD());
            pstmt.setString(6, question.getCorrectAnswer().getCode());
            pstmt.setString(7, question.getDifficulty().getCode());
            pstmt.setString(8, question.getSubject());

            // 处理创建者ID，如果为null则设置为1（默认管理员）
            Integer createdBy = question.getCreatedBy();
            if (createdBy == null) {
                createdBy = 1; // 默认管理员ID
            }
            pstmt.setInt(9, createdBy);

            int result = pstmt.executeUpdate();
            DatabaseUtil.commit(conn);

            return result > 0;

        } catch (SQLException e) {
            System.err.println("添加题目失败: " + e.getMessage());
            e.printStackTrace(); // 添加详细错误信息
            DatabaseUtil.rollback(conn);
            return false;
        } finally {
            DatabaseUtil.closeAll(conn, pstmt, null);
        }
    }
    
    /**
     * 更新题目
     * @param question 题目对象
     * @return true如果更新成功，否则false
     */
    public boolean updateQuestion(Question question) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            String sql = "UPDATE questions SET question_content = ?, option_a = ?, option_b = ?, option_c = ?, option_d = ?, correct_answer = ?, difficulty = ?, subject = ? WHERE question_id = ?";
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, question.getQuestionContent());
            pstmt.setString(2, question.getOptionA());
            pstmt.setString(3, question.getOptionB());
            pstmt.setString(4, question.getOptionC());
            pstmt.setString(5, question.getOptionD());
            pstmt.setString(6, question.getCorrectAnswer().getCode());
            pstmt.setString(7, question.getDifficulty().getCode());
            pstmt.setString(8, question.getSubject());
            pstmt.setInt(9, question.getQuestionId());
            
            int result = pstmt.executeUpdate();
            DatabaseUtil.commit(conn);
            
            return result > 0;
            
        } catch (SQLException e) {
            System.err.println("更新题目失败: " + e.getMessage());
            DatabaseUtil.rollback(conn);
            return false;
        } finally {
            DatabaseUtil.closeAll(conn, pstmt, null);
        }
    }
    
    /**
     * 删除题目
     * @param questionId 题目ID
     * @return true如果删除成功，否则false
     */
    public boolean deleteQuestion(int questionId) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            String sql = "UPDATE questions SET status = 'INACTIVE' WHERE question_id = ?";
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, questionId);
            
            int result = pstmt.executeUpdate();
            DatabaseUtil.commit(conn);
            
            return result > 0;
            
        } catch (SQLException e) {
            System.err.println("删除题目失败: " + e.getMessage());
            DatabaseUtil.rollback(conn);
            return false;
        } finally {
            DatabaseUtil.closeAll(conn, pstmt, null);
        }
    }
    
    /**
     * 保存考试记录
     * @param examRecord 考试记录
     * @return 保存的记录ID，失败返回null
     */
    public Integer saveExamRecord(ExamRecord examRecord) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            String sql = "INSERT INTO exam_records (user_id, user_name, total_questions, correct_answers, score, time_used, exam_duration, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            pstmt.setInt(1, examRecord.getUserId());
            pstmt.setString(2, examRecord.getUserName());
            pstmt.setInt(3, examRecord.getTotalQuestions());
            pstmt.setInt(4, examRecord.getCorrectAnswers());
            pstmt.setBigDecimal(5, examRecord.getScore());
            pstmt.setInt(6, examRecord.getTimeUsed());
            pstmt.setInt(7, examRecord.getExamDuration());
            pstmt.setString(8, examRecord.getStatus().getCode());
            
            int result = pstmt.executeUpdate();
            
            if (result > 0) {
                rs = pstmt.getGeneratedKeys();
                if (rs.next()) {
                    int recordId = rs.getInt(1);
                    examRecord.setRecordId(recordId);
                    
                    // 保存考试详情
                    List<ExamDetail> examDetails = examRecord.getExamDetails();
                    if (examDetails != null && !examDetails.isEmpty()) {
                        // 设置每个详情的recordId
                        for (ExamDetail detail : examDetails) {
                            detail.setRecordId(recordId);
                        }
                        if (saveExamDetails(conn, examDetails)) {
                            DatabaseUtil.commit(conn);
                            return recordId;
                        } else {
                            System.err.println("保存考试详情失败");
                            DatabaseUtil.rollback(conn);
                            return null;
                        }
                    } else {
                        // 没有考试详情也算成功
                        DatabaseUtil.commit(conn);
                        return recordId;
                    }
                }
            }
            
            DatabaseUtil.rollback(conn);
            return null;
            
        } catch (SQLException e) {
            System.err.println("保存考试记录失败: " + e.getMessage());
            e.printStackTrace();
            DatabaseUtil.rollback(conn);
            return null;
        } catch (Exception e) {
            System.err.println("保存考试记录异常: " + e.getMessage());
            e.printStackTrace();
            DatabaseUtil.rollback(conn);
            return null;
        } finally {
            DatabaseUtil.closeAll(conn, pstmt, rs);
        }
    }
    
    /**
     * 保存考试详情
     * @param conn 数据库连接
     * @param examDetails 考试详情列表
     * @return true如果保存成功，否则false
     */
    private boolean saveExamDetails(Connection conn, List<ExamDetail> examDetails) {
        if (examDetails == null || examDetails.isEmpty()) {
            return true;
        }

        PreparedStatement pstmt = null;

        try {
            String sql = "INSERT INTO exam_details (record_id, question_id, user_answer, correct_answer, is_correct) VALUES (?, ?, ?, ?, ?)";
            pstmt = conn.prepareStatement(sql);

            for (ExamDetail detail : examDetails) {
                // 验证必要字段
                if (detail.getRecordId() == null || detail.getQuestionId() == null) {
                    System.err.println("考试详情数据不完整: recordId=" + detail.getRecordId() +
                                     ", questionId=" + detail.getQuestionId());
                    continue;
                }

                pstmt.setInt(1, detail.getRecordId());
                pstmt.setInt(2, detail.getQuestionId());
                pstmt.setString(3, detail.getUserAnswer() != null ? detail.getUserAnswer().getCode() : null);
                pstmt.setString(4, detail.getCorrectAnswer() != null ? detail.getCorrectAnswer().getCode() : null);
                pstmt.setBoolean(5, detail.isCorrect() != null ? detail.isCorrect() : false);
                pstmt.addBatch();
            }

            int[] results = pstmt.executeBatch();
            return true;

        } catch (SQLException e) {
            System.err.println("保存考试详情失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            DatabaseUtil.closePreparedStatement(pstmt);
        }
    }
    
    /**
     * 获取用户考试记录
     * @param userId 用户ID
     * @return 考试记录列表
     */
    public List<ExamRecord> getUserExamRecords(int userId) {
        List<ExamRecord> records = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            String sql = "SELECT * FROM exam_records WHERE user_id = ? ORDER BY exam_time DESC";
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, userId);
            
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                records.add(mapResultSetToExamRecord(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("获取考试记录失败: " + e.getMessage());
        } finally {
            DatabaseUtil.closeAll(conn, pstmt, rs);
        }
        
        return records;
    }
    
    /**
     * 获取所有考试记录（管理员用）
     * @return 考试记录列表
     */
    public List<ExamRecord> getAllExamRecords() {
        List<ExamRecord> records = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DatabaseUtil.getConnection();
            String sql = "SELECT * FROM exam_records ORDER BY exam_time DESC";
            pstmt = conn.prepareStatement(sql);

            rs = pstmt.executeQuery();

            while (rs.next()) {
                try {
                    records.add(mapResultSetToExamRecord(rs));
                } catch (Exception e) {
                    System.err.println("映射考试记录数据失败: " + e.getMessage());
                    e.printStackTrace();
                }
            }

        } catch (SQLException e) {
            System.err.println("获取所有考试记录失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeAll(conn, pstmt, rs);
        }

        return records;
    }
    
    // 辅助方法：将ResultSet映射为User对象
    private User mapResultSetToUser(ResultSet rs) throws SQLException {
        User user = new User();
        user.setUserId(rs.getInt("user_id"));
        user.setUsername(rs.getString("username"));
        user.setPassword(rs.getString("password"));
        user.setRealName(rs.getString("real_name"));
        user.setUserType(User.UserType.valueOf(rs.getString("user_type")));
        user.setEmail(rs.getString("email"));
        user.setPhone(rs.getString("phone"));
        user.setCreatedTime(rs.getTimestamp("created_time"));
        user.setUpdatedTime(rs.getTimestamp("updated_time"));
        user.setStatus(User.UserStatus.valueOf(rs.getString("status")));
        return user;
    }
    
    // 辅助方法：将ResultSet映射为Question对象
    private Question mapResultSetToQuestion(ResultSet rs) throws SQLException {
        Question question = new Question();
        question.setQuestionId(rs.getInt("question_id"));
        question.setQuestionContent(rs.getString("question_content"));
        question.setOptionA(rs.getString("option_a"));
        question.setOptionB(rs.getString("option_b"));
        question.setOptionC(rs.getString("option_c"));
        question.setOptionD(rs.getString("option_d"));
        question.setCorrectAnswer(Question.Answer.fromCode(rs.getString("correct_answer")));
        question.setDifficulty(Question.Difficulty.valueOf(rs.getString("difficulty")));
        question.setSubject(rs.getString("subject"));
        question.setCreatedBy(rs.getInt("created_by"));
        question.setCreatedTime(rs.getTimestamp("created_time"));
        question.setUpdatedTime(rs.getTimestamp("updated_time"));
        question.setStatus(Question.Status.valueOf(rs.getString("status")));
        return question;
    }
    
    // 辅助方法：将ResultSet映射为ExamRecord对象
    private ExamRecord mapResultSetToExamRecord(ResultSet rs) throws SQLException {
        ExamRecord record = new ExamRecord();
        record.setRecordId(rs.getInt("record_id"));
        record.setUserId(rs.getInt("user_id"));
        record.setUserName(rs.getString("user_name"));
        record.setExamTime(rs.getTimestamp("exam_time"));
        record.setTotalQuestions(rs.getInt("total_questions"));
        record.setCorrectAnswers(rs.getInt("correct_answers"));
        record.setScore(rs.getBigDecimal("score"));
        record.setTimeUsed(rs.getInt("time_used"));
        record.setExamDuration(rs.getInt("exam_duration"));
        record.setStatus(ExamRecord.ExamStatus.valueOf(rs.getString("status")));
        return record;
    }
}
