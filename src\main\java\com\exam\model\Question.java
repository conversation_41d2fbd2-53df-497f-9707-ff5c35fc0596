package com.exam.model;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 试题实体类
 * 实现MVC模式中的Model层
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class Question implements Serializable {
    private static final long serialVersionUID = 1L;
    
    // 答案选项枚举
    public enum Answer {
        A("A", "选项A"),
        B("B", "选项B"),
        C("C", "选项C"),
        D("D", "选项D");
        
        private final String code;
        private final String description;
        
        Answer(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() { return code; }
        public String getDescription() { return description; }
        
        public static Answer fromCode(String code) {
            for (Answer answer : values()) {
                if (answer.code.equals(code)) {
                    return answer;
                }
            }
            return null;
        }
    }
    
    // 难度等级枚举
    public enum Difficulty {
        EASY("EASY", "简单"),
        MEDIUM("MEDIUM", "中等"),
        HARD("HARD", "困难");
        
        private final String code;
        private final String description;
        
        Difficulty(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() { return code; }
        public String getDescription() { return description; }
    }
    
    // 状态枚举
    public enum Status {
        ACTIVE("ACTIVE", "启用"),
        INACTIVE("INACTIVE", "禁用");
        
        private final String code;
        private final String description;
        
        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() { return code; }
        public String getDescription() { return description; }
    }
    
    // 试题属性
    private Integer questionId;        // 题目编号（主键）
    private String questionContent;    // 题目内容
    private String optionA;           // 选项A
    private String optionB;           // 选项B
    private String optionC;           // 选项C
    private String optionD;           // 选项D
    private Answer correctAnswer;     // 正确答案
    private Difficulty difficulty;    // 难度等级
    private String subject;           // 科目
    private Integer createdBy;        // 创建者ID
    private Timestamp createdTime;    // 创建时间
    private Timestamp updatedTime;    // 更新时间
    private Status status;            // 状态
    
    // 默认构造函数
    public Question() {
        this.difficulty = Difficulty.MEDIUM;
        this.status = Status.ACTIVE;
        this.subject = "计算机基础";
    }
    
    // 带参构造函数
    public Question(String questionContent, String optionA, String optionB, 
                   String optionC, String optionD, Answer correctAnswer) {
        this();
        this.questionContent = questionContent;
        this.optionA = optionA;
        this.optionB = optionB;
        this.optionC = optionC;
        this.optionD = optionD;
        this.correctAnswer = correctAnswer;
    }
    
    // Getter和Setter方法
    public Integer getQuestionId() {
        return questionId;
    }
    
    public void setQuestionId(Integer questionId) {
        this.questionId = questionId;
    }

    /**
     * 设置题目ID（支持多种数字类型）
     * @param questionId 题目ID（可以是Integer、Double、Long等）
     */
    public void setQuestionId(Object questionId) {
        if (questionId == null) {
            this.questionId = null;
        } else if (questionId instanceof Integer) {
            this.questionId = (Integer) questionId;
        } else if (questionId instanceof Number) {
            this.questionId = ((Number) questionId).intValue();
        } else {
            try {
                this.questionId = Integer.valueOf(questionId.toString());
            } catch (NumberFormatException e) {
                System.err.println("无法转换questionId: " + questionId + " (类型: " + questionId.getClass().getName() + ")");
                this.questionId = null;
            }
        }
    }
    
    public String getQuestionContent() {
        return questionContent;
    }
    
    public void setQuestionContent(String questionContent) {
        this.questionContent = questionContent;
    }
    
    public String getOptionA() {
        return optionA;
    }
    
    public void setOptionA(String optionA) {
        this.optionA = optionA;
    }
    
    public String getOptionB() {
        return optionB;
    }
    
    public void setOptionB(String optionB) {
        this.optionB = optionB;
    }
    
    public String getOptionC() {
        return optionC;
    }
    
    public void setOptionC(String optionC) {
        this.optionC = optionC;
    }
    
    public String getOptionD() {
        return optionD;
    }
    
    public void setOptionD(String optionD) {
        this.optionD = optionD;
    }
    
    public Answer getCorrectAnswer() {
        return correctAnswer;
    }
    
    public void setCorrectAnswer(Answer correctAnswer) {
        this.correctAnswer = correctAnswer;
    }
    
    public Difficulty getDifficulty() {
        return difficulty;
    }
    
    public void setDifficulty(Difficulty difficulty) {
        this.difficulty = difficulty;
    }
    
    public String getSubject() {
        return subject;
    }
    
    public void setSubject(String subject) {
        this.subject = subject;
    }
    
    public Integer getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * 设置创建者ID（支持多种数字类型）
     * @param createdBy 创建者ID（可以是Integer、Double、Long等）
     */
    public void setCreatedBy(Object createdBy) {
        if (createdBy == null) {
            this.createdBy = null;
        } else if (createdBy instanceof Integer) {
            this.createdBy = (Integer) createdBy;
        } else if (createdBy instanceof Number) {
            this.createdBy = ((Number) createdBy).intValue();
        } else {
            try {
                this.createdBy = Integer.valueOf(createdBy.toString());
            } catch (NumberFormatException e) {
                System.err.println("无法转换createdBy: " + createdBy + " (类型: " + createdBy.getClass().getName() + ")");
                this.createdBy = null;
            }
        }
    }
    
    public Timestamp getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(Timestamp createdTime) {
        this.createdTime = createdTime;
    }
    
    public Timestamp getUpdatedTime() {
        return updatedTime;
    }
    
    public void setUpdatedTime(Timestamp updatedTime) {
        this.updatedTime = updatedTime;
    }
    
    public Status getStatus() {
        return status;
    }
    
    public void setStatus(Status status) {
        this.status = status;
    }
    
    // 业务方法
    
    /**
     * 根据选项代码获取选项内容
     * @param answer 答案选项
     * @return 选项内容
     */
    public String getOptionByAnswer(Answer answer) {
        if (answer == null) return "";
        switch (answer) {
            case A: return optionA;
            case B: return optionB;
            case C: return optionC;
            case D: return optionD;
            default: return "";
        }
    }
    
    /**
     * 检查答案是否正确
     * @param userAnswer 用户答案
     * @return true如果正确，否则false
     */
    public boolean isCorrect(Answer userAnswer) {
        return correctAnswer != null && correctAnswer.equals(userAnswer);
    }
    
    /**
     * 验证题目信息是否完整
     * @return true如果信息完整，否则false
     */
    public boolean isValid() {
        return questionContent != null && !questionContent.trim().isEmpty() &&
               optionA != null && !optionA.trim().isEmpty() &&
               optionB != null && !optionB.trim().isEmpty() &&
               optionC != null && !optionC.trim().isEmpty() &&
               optionD != null && !optionD.trim().isEmpty() &&
               correctAnswer != null;
    }
    
    /**
     * 检查题目是否启用
     * @return true如果启用，否则false
     */
    public boolean isActive() {
        return Status.ACTIVE.equals(this.status);
    }
    
    @Override
    public String toString() {
        return "Question{" +
                "questionId=" + questionId +
                ", questionContent='" + questionContent + '\'' +
                ", correctAnswer=" + correctAnswer +
                ", difficulty=" + difficulty +
                ", subject='" + subject + '\'' +
                ", status=" + status +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Question question = (Question) obj;
        return questionId != null && questionId.equals(question.questionId);
    }
    
    @Override
    public int hashCode() {
        return questionId != null ? questionId.hashCode() : 0;
    }
}
