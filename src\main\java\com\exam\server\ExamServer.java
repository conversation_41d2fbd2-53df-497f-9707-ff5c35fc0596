package com.exam.server;

import com.exam.util.DatabaseUtil;
import com.exam.util.FileUtil;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 考试系统服务器主类
 * 实现C/S架构的服务器端
 * 支持多客户端并发连接
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ExamServer {
    
    // 服务器配置
    private static final int DEFAULT_PORT = 8888;
    private static final int MAX_CLIENTS = 100;
    
    // 服务器组件
    private ServerSocket serverSocket;
    private DatabaseManager dbManager;
    private ExecutorService threadPool;
    private boolean isRunning;
    
    // 客户端管理
    private ConcurrentHashMap<String, ClientHandler> clientHandlers;
    
    // 服务器统计信息
    private int totalConnections = 0;
    private int activeConnections = 0;
    
    /**
     * 构造函数
     */
    public ExamServer() {
        this.dbManager = new DatabaseManager();
        this.threadPool = Executors.newFixedThreadPool(MAX_CLIENTS);
        this.clientHandlers = new ConcurrentHashMap<>();
        this.isRunning = false;
    }
    
    /**
     * 启动服务器
     * @param port 服务器端口
     */
    public void start(int port) {
        try {
            // 初始化数据库
            if (!initializeDatabase()) {
                System.err.println("数据库初始化失败，服务器启动中止");
                return;
            }

            // 创建服务器Socket
            serverSocket = new ServerSocket(port);
            isRunning = true;
            
            System.out.println("=== 考试系统服务器启动 ===");
            System.out.println("服务器端口: " + port);
            System.out.println("最大客户端数: " + MAX_CLIENTS);
            System.out.println("服务器状态: 运行中");
            System.out.println("等待客户端连接...");
            
            // 记录服务器启动日志
            FileUtil.logMessage("INFO", "考试系统服务器启动，端口: " + port);
            
            // 主循环：接受客户端连接
            while (isRunning) {
                try {
                    Socket clientSocket = serverSocket.accept();
                    handleNewClient(clientSocket);
                    
                } catch (IOException e) {
                    if (isRunning) {
                        System.err.println("接受客户端连接失败: " + e.getMessage());
                        FileUtil.logMessage("ERROR", "接受客户端连接失败: " + e.getMessage());
                    }
                }
            }
            
        } catch (IOException e) {
            System.err.println("服务器启动失败: " + e.getMessage());
            FileUtil.logMessage("ERROR", "服务器启动失败: " + e.getMessage());
        } finally {
            shutdown();
        }
    }
    
    /**
     * 处理新客户端连接
     * @param clientSocket 客户端Socket
     */
    private void handleNewClient(Socket clientSocket) {
        try {
            // 检查连接数限制
            if (activeConnections >= MAX_CLIENTS) {
                System.out.println("客户端连接数已达上限，拒绝连接: " + 
                                 clientSocket.getRemoteSocketAddress());
                clientSocket.close();
                return;
            }
            
            // 创建客户端处理器
            ClientHandler clientHandler = new ClientHandler(clientSocket, dbManager);
            
            // 将处理器提交到线程池
            threadPool.submit(() -> {
                try {
                    // 更新连接统计
                    totalConnections++;
                    activeConnections++;
                    clientHandlers.put(clientHandler.getSessionId(), clientHandler);
                    
                    System.out.println("客户端连接成功: " + clientSocket.getRemoteSocketAddress() + 
                                     " (活跃连接数: " + activeConnections + ")");
                    
                    // 运行客户端处理器
                    clientHandler.run();
                    
                } finally {
                    // 清理连接
                    activeConnections--;
                    clientHandlers.remove(clientHandler.getSessionId());
                    
                    System.out.println("客户端连接关闭: " + clientSocket.getRemoteSocketAddress() + 
                                     " (活跃连接数: " + activeConnections + ")");
                }
            });
            
        } catch (IOException e) {
            System.err.println("处理新客户端连接失败: " + e.getMessage());
        }
    }

    /**
     * 初始化数据库
     * @return true如果初始化成功，否则false
     */
    private boolean initializeDatabase() {
        System.out.println("正在初始化数据库...");

        // 测试数据库连接
        if (!DatabaseUtil.testConnection()) {
            System.err.println("数据库连接测试失败");
            return false;
        }

        System.out.println("数据库初始化完成");
        return true;
    }

    /**
     * 停止服务器
     */
    public void stop() {
        System.out.println("正在停止服务器...");
        isRunning = false;
        
        try {
            if (serverSocket != null && !serverSocket.isClosed()) {
                serverSocket.close();
            }
        } catch (IOException e) {
            System.err.println("关闭服务器Socket失败: " + e.getMessage());
        }
    }
    
    /**
     * 关闭服务器并清理资源
     */
    private void shutdown() {
        System.out.println("正在关闭服务器...");
        
        // 停止所有客户端处理器
        for (ClientHandler handler : clientHandlers.values()) {
            handler.stop();
        }
        clientHandlers.clear();
        
        // 关闭线程池
        if (threadPool != null && !threadPool.isShutdown()) {
            threadPool.shutdown();
            try {
                if (!threadPool.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                    threadPool.shutdownNow();
                }
            } catch (InterruptedException e) {
                threadPool.shutdownNow();
            }
        }
        
        // 关闭服务器Socket
        try {
            if (serverSocket != null && !serverSocket.isClosed()) {
                serverSocket.close();
            }
        } catch (IOException e) {
            System.err.println("关闭服务器Socket失败: " + e.getMessage());
        }
        
        // 记录服务器关闭日志
        FileUtil.logMessage("INFO", "考试系统服务器关闭，总连接数: " + totalConnections);
        
        System.out.println("服务器已关闭");
        System.out.println("总连接数: " + totalConnections);
    }
    
    /**
     * 获取服务器状态信息
     * @return 状态信息字符串
     */
    public String getServerStatus() {
        return String.format("服务器状态: %s, 活跃连接: %d, 总连接: %d", 
                           isRunning ? "运行中" : "已停止", 
                           activeConnections, 
                           totalConnections);
    }
    
    /**
     * 广播消息给所有客户端（管理功能）
     * @param message 广播消息
     */
    public void broadcastMessage(String message) {
        System.out.println("广播消息: " + message);
        // 这里可以实现向所有客户端发送消息的功能
        // 由于简化实现，暂时只记录日志
        FileUtil.logMessage("INFO", "广播消息: " + message);
    }
    
    /**
     * 获取活跃连接数
     * @return 活跃连接数
     */
    public int getActiveConnections() {
        return activeConnections;
    }
    
    /**
     * 获取总连接数
     * @return 总连接数
     */
    public int getTotalConnections() {
        return totalConnections;
    }
    
    /**
     * 主方法
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 设置系统属性
        System.setProperty("file.encoding", "UTF-8");
        
        // 解析命令行参数
        int port = DEFAULT_PORT;
        if (args.length > 0) {
            try {
                port = Integer.parseInt(args[0]);
                if (port < 1024 || port > 65535) {
                    System.err.println("端口号必须在1024-65535之间");
                    return;
                }
            } catch (NumberFormatException e) {
                System.err.println("无效的端口号: " + args[0]);
                return;
            }
        }
        
        // 创建并启动服务器
        ExamServer server = new ExamServer();
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("\n接收到关闭信号...");
            server.stop();
        }));
        
        // 启动服务器
        server.start(port);
    }
}
