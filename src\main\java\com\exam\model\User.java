package com.exam.model;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 用户实体类
 * 实现MVC模式中的Model层
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class User implements Serializable {
    private static final long serialVersionUID = 1L;
    
    // 用户类型枚举
    public enum UserType {
        STUDENT("STUDENT", "学生"),
        ADMIN("ADMIN", "管理员");
        
        private final String code;
        private final String description;
        
        UserType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() { return code; }
        public String getDescription() { return description; }
    }
    
    // 用户状态枚举
    public enum UserStatus {
        ACTIVE("ACTIVE", "激活"),
        INACTIVE("INACTIVE", "未激活");
        
        private final String code;
        private final String description;
        
        UserStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() { return code; }
        public String getDescription() { return description; }
    }
    
    // 用户属性
    private Integer userId;          // 用户编号（主键）
    private String username;         // 用户名
    private String password;         // 密码
    private String realName;         // 真实姓名
    private UserType userType;       // 用户类型
    private String email;            // 邮箱
    private String phone;            // 电话
    private Timestamp createdTime;   // 创建时间
    private Timestamp updatedTime;   // 更新时间
    private UserStatus status;       // 状态
    
    // 默认构造函数
    public User() {
        this.userType = UserType.STUDENT;
        this.status = UserStatus.ACTIVE;
    }
    
    // 带参构造函数
    public User(String username, String password, String realName, UserType userType) {
        this();
        this.username = username;
        this.password = password;
        this.realName = realName;
        this.userType = userType;
    }
    
    // Getter和Setter方法
    public Integer getUserId() {
        return userId;
    }
    
    public void setUserId(Integer userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getRealName() {
        return realName;
    }
    
    public void setRealName(String realName) {
        this.realName = realName;
    }
    
    public UserType getUserType() {
        return userType;
    }
    
    public void setUserType(UserType userType) {
        this.userType = userType;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public Timestamp getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(Timestamp createdTime) {
        this.createdTime = createdTime;
    }
    
    public Timestamp getUpdatedTime() {
        return updatedTime;
    }
    
    public void setUpdatedTime(Timestamp updatedTime) {
        this.updatedTime = updatedTime;
    }
    
    public UserStatus getStatus() {
        return status;
    }
    
    public void setStatus(UserStatus status) {
        this.status = status;
    }
    
    // 业务方法
    
    /**
     * 检查是否为管理员
     * @return true如果是管理员，否则false
     */
    public boolean isAdmin() {
        return UserType.ADMIN.equals(this.userType);
    }
    
    /**
     * 检查是否为学生
     * @return true如果是学生，否则false
     */
    public boolean isStudent() {
        return UserType.STUDENT.equals(this.userType);
    }
    
    /**
     * 检查用户是否激活
     * @return true如果用户激活，否则false
     */
    public boolean isActive() {
        return UserStatus.ACTIVE.equals(this.status);
    }
    
    /**
     * 验证用户信息是否完整
     * @return true如果信息完整，否则false
     */
    public boolean isValid() {
        return username != null && !username.trim().isEmpty() &&
               password != null && !password.trim().isEmpty() &&
               realName != null && !realName.trim().isEmpty() &&
               userType != null;
    }
    
    @Override
    public String toString() {
        return "User{" +
                "userId=" + userId +
                ", username='" + username + '\'' +
                ", realName='" + realName + '\'' +
                ", userType=" + userType +
                ", email='" + email + '\'' +
                ", phone='" + phone + '\'' +
                ", status=" + status +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        User user = (User) obj;
        return userId != null && userId.equals(user.userId);
    }
    
    @Override
    public int hashCode() {
        return userId != null ? userId.hashCode() : 0;
    }
}
