-- 考试系统数据库初始化脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS exam_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE exam_system;

-- 删除已存在的表（按依赖关系顺序）
DROP TABLE IF EXISTS exam_details;
DROP TABLE IF EXISTS exam_records;
DROP TABLE IF EXISTS questions;
DROP TABLE IF EXISTS users;

-- 创建用户表
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户编号',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    user_type ENUM('STUDENT', 'ADMIN') NOT NULL DEFAULT 'STUDENT' COMMENT '用户类型',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',
    INDEX idx_username (username),
    INDEX idx_user_type (user_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建试题表
CREATE TABLE questions (
    question_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '题目编号',
    question_content TEXT NOT NULL COMMENT '题目内容',
    option_a VARCHAR(500) NOT NULL COMMENT '选项A',
    option_b VARCHAR(500) NOT NULL COMMENT '选项B',
    option_c VARCHAR(500) NOT NULL COMMENT '选项C',
    option_d VARCHAR(500) NOT NULL COMMENT '选项D',
    correct_answer ENUM('A', 'B', 'C', 'D') NOT NULL COMMENT '正确答案',
    difficulty ENUM('EASY', 'MEDIUM', 'HARD') DEFAULT 'MEDIUM' COMMENT '难度等级',
    subject VARCHAR(100) DEFAULT '计算机基础' COMMENT '科目',
    created_by INT COMMENT '创建者ID',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',
    INDEX idx_difficulty (difficulty),
    INDEX idx_subject (subject),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='试题表';

-- 创建考试记录表
CREATE TABLE exam_records (
    record_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '记录编号',
    user_id INT NOT NULL COMMENT '用户编号',
    user_name VARCHAR(50) NOT NULL COMMENT '用户姓名',
    exam_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '考试时间',
    total_questions INT NOT NULL DEFAULT 0 COMMENT '总题数',
    correct_answers INT NOT NULL DEFAULT 0 COMMENT '正确答案数',
    score DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '考试成绩',
    time_used INT NOT NULL DEFAULT 0 COMMENT '用时（秒）',
    exam_duration INT NOT NULL DEFAULT 1800 COMMENT '考试时长（秒）',
    status ENUM('COMPLETED', 'TIMEOUT', 'INTERRUPTED') DEFAULT 'COMPLETED' COMMENT '考试状态',
    INDEX idx_user_id (user_id),
    INDEX idx_exam_time (exam_time),
    INDEX idx_score (score),
    INDEX idx_status (status),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试记录表';

-- 创建考试详情表
CREATE TABLE exam_details (
    detail_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '详情编号',
    record_id INT NOT NULL COMMENT '考试记录编号',
    question_id INT NOT NULL COMMENT '题目编号',
    user_answer ENUM('A', 'B', 'C', 'D') COMMENT '用户答案',
    correct_answer ENUM('A', 'B', 'C', 'D') NOT NULL COMMENT '正确答案',
    is_correct BOOLEAN DEFAULT FALSE COMMENT '是否正确',
    answer_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '答题时间',
    INDEX idx_record_id (record_id),
    INDEX idx_question_id (question_id),
    INDEX idx_is_correct (is_correct),
    FOREIGN KEY (record_id) REFERENCES exam_records(record_id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试详情表';

-- 插入默认管理员用户
INSERT INTO users (username, password, real_name, user_type, email, phone) VALUES
('admin', '123456', '系统管理员', 'ADMIN', '<EMAIL>', '13800138000'),
('student1', '123456', '张三', 'STUDENT', '<EMAIL>', '13800138001'),
('student2', '123456', '李四', 'STUDENT', '<EMAIL>', '13800138002');

-- 插入示例题目
INSERT INTO questions (question_content, option_a, option_b, option_c, option_d, correct_answer, difficulty, subject, created_by) VALUES
('Java中哪个关键字用于定义常量？', 'const', 'final', 'static', 'constant', 'B', 'EASY', 'Java基础', 1),
('下列哪个不是Java的基本数据类型？', 'int', 'float', 'String', 'boolean', 'C', 'EASY', 'Java基础', 1),
('Java中的继承使用哪个关键字？', 'extends', 'implements', 'inherit', 'super', 'A', 'EASY', 'Java基础', 1),
('下列哪个是正确的Java方法重载？', '方法名相同，参数不同', '方法名不同，参数相同', '方法名相同，返回值不同', '方法名和参数都相同', 'A', 'MEDIUM', 'Java基础', 1),
('Java中的多态性主要通过什么实现？', '继承和重写', '封装', '抽象', '接口', 'A', 'MEDIUM', 'Java基础', 1),
('下列哪个集合类是线程安全的？', 'ArrayList', 'HashMap', 'Vector', 'LinkedList', 'C', 'MEDIUM', 'Java基础', 1),
('Java虚拟机的内存模型中，哪个区域存储对象实例？', '方法区', '堆内存', '栈内存', '程序计数器', 'B', 'HARD', 'Java基础', 1),
('下列关于Java垃圾回收的说法，哪个是正确的？', '程序员可以强制进行垃圾回收', '垃圾回收只回收堆内存', '垃圾回收会影响程序性能', '以上都正确', 'D', 'HARD', 'Java基础', 1),
('MySQL中用于创建索引的SQL语句是？', 'CREATE INDEX', 'ADD INDEX', 'NEW INDEX', 'MAKE INDEX', 'A', 'EASY', '数据库', 1),
('下列哪个不是关系数据库的特点？', '数据独立性', '数据一致性', '数据冗余性', '数据完整性', 'C', 'MEDIUM', '数据库', 1);

-- 插入示例考试记录
INSERT INTO exam_records (user_id, user_name, total_questions, correct_answers, score, time_used, exam_duration, status) VALUES
(2, '张三', 5, 4, 80.00, 900, 1800, 'COMPLETED'),
(3, '李四', 5, 3, 60.00, 1200, 1800, 'COMPLETED');

-- 插入示例考试详情
INSERT INTO exam_details (record_id, question_id, user_answer, correct_answer, is_correct) VALUES
(1, 1, 'B', 'B', TRUE),
(1, 2, 'C', 'C', TRUE),
(1, 3, 'A', 'A', TRUE),
(1, 4, 'B', 'A', FALSE),
(1, 5, 'A', 'A', TRUE),
(2, 1, 'B', 'B', TRUE),
(2, 2, 'A', 'C', FALSE),
(2, 3, 'A', 'A', TRUE),
(2, 4, 'A', 'A', TRUE),
(2, 5, 'B', 'A', FALSE);

-- 显示初始化完成信息
SELECT '数据库初始化完成' AS message;
SELECT COUNT(*) AS user_count FROM users;
SELECT COUNT(*) AS question_count FROM questions;
SELECT COUNT(*) AS record_count FROM exam_records;
