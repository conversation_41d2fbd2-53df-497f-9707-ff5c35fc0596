package com.exam.controller;

import com.exam.client.ExamClient;
import com.exam.model.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * 考试控制器
 * 实现MVC模式中的Controller层
 * 处理考试相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ExamController {
    
    private ExamClient client;
    
    /**
     * 构造函数
     * @param client 客户端对象
     */
    public ExamController(ExamClient client) {
        this.client = client;
    }
    
    /**
     * 开始考试
     * @param questionCount 题目数量
     * @param examDuration 考试时长（秒）
     * @return 考试题目响应消息
     */
    public Message startExam(int questionCount, int examDuration) {
        // 验证参数
        if (questionCount <= 0) {
            return createErrorMessage("题目数量必须大于0");
        }
        
        if (questionCount > 20) {
            return createErrorMessage("题目数量不能超过20道");
        }
        
        if (examDuration <= 0) {
            return createErrorMessage("考试时长必须大于0");
        }
        
        if (examDuration > 7200) { // 最长2小时
            return createErrorMessage("考试时长不能超过2小时");
        }
        
        try {
            Message request = new Message(Message.MessageType.EXAM_START_REQUEST);
            request.addParam("questionCount", questionCount);
            request.addParam("examDuration", examDuration);
            
            return client.sendMessage(request);
            
        } catch (IOException e) {
            return createErrorMessage("开始考试失败: " + e.getMessage());
        }
    }
    
    /**
     * 提交考试
     * @param examRecord 考试记录
     * @return 提交结果消息
     */
    public Message submitExam(ExamRecord examRecord) {
        // 验证考试记录
        String validationError = validateExamRecord(examRecord);
        if (validationError != null) {
            return createErrorMessage(validationError);
        }
        
        try {
            Message request = new Message(Message.MessageType.EXAM_SUBMIT_REQUEST, examRecord);
            return client.sendMessage(request);
            
        } catch (IOException e) {
            return createErrorMessage("提交考试失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取成绩列表
     * @return 成绩列表响应消息
     */
    public Message getScoreList() {
        try {
            Message request = new Message(Message.MessageType.SCORE_LIST_REQUEST);
            return client.sendMessage(request);

        } catch (IOException e) {
            return createErrorMessage("获取成绩列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取可用题目数量
     * @return 可用题目数量响应消息
     */
    public Message getAvailableQuestionCount() {
        try {
            Message request = new Message(Message.MessageType.QUESTION_COUNT_REQUEST);
            return client.sendMessage(request);

        } catch (IOException e) {
            return createErrorMessage("获取题目数量失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取成绩详情
     * @param recordId 考试记录ID
     * @return 成绩详情响应消息
     */
    public Message getScoreDetail(Integer recordId) {
        if (recordId == null) {
            return createErrorMessage("考试记录ID不能为空");
        }
        
        try {
            Message request = new Message(Message.MessageType.SCORE_DETAIL_REQUEST);
            request.addParam("recordId", recordId);
            return client.sendMessage(request);
            
        } catch (IOException e) {
            return createErrorMessage("获取成绩详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建考试记录
     * @param questions 考试题目列表
     * @param examDuration 考试时长
     * @return 考试记录对象
     */
    public ExamRecord createExamRecord(List<Question> questions, int examDuration) {
        User currentUser = client.getCurrentUser();
        if (currentUser == null) {
            return null;
        }
        
        ExamRecord examRecord = new ExamRecord();
        examRecord.setUserId(currentUser.getUserId());
        examRecord.setUserName(currentUser.getRealName());
        examRecord.setTotalQuestions(questions.size());
        examRecord.setExamDuration(examDuration);
        examRecord.setCorrectAnswers(0);
        examRecord.setScore(BigDecimal.ZERO);
        examRecord.setTimeUsed(0);
        examRecord.setStatus(ExamRecord.ExamStatus.COMPLETED);
        
        return examRecord;
    }
    
    /**
     * 计算考试成绩
     * @param examDetails 考试详情列表
     * @return 考试记录对象
     */
    public ExamRecord calculateExamScore(List<ExamDetail> examDetails, int timeUsed, int examDuration) {
        User currentUser = client.getCurrentUser();
        if (currentUser == null || examDetails == null) {
            return null;
        }
        
        ExamRecord examRecord = new ExamRecord();
        examRecord.setUserId(currentUser.getUserId());
        examRecord.setUserName(currentUser.getRealName());
        examRecord.setTotalQuestions(examDetails.size());
        examRecord.setTimeUsed(timeUsed);
        examRecord.setExamDuration(examDuration);
        examRecord.setExamDetails(examDetails);
        
        // 计算正确答案数
        int correctCount = 0;
        for (ExamDetail detail : examDetails) {
            if (detail.isCorrect()) {
                correctCount++;
            }
        }
        examRecord.setCorrectAnswers(correctCount);
        
        // 计算成绩
        examRecord.calculateScore();
        
        // 设置考试状态
        if (timeUsed >= examDuration) {
            examRecord.setStatus(ExamRecord.ExamStatus.TIMEOUT);
        } else {
            examRecord.setStatus(ExamRecord.ExamStatus.COMPLETED);
        }
        
        return examRecord;
    }
    
    /**
     * 创建考试详情
     * @param recordId 考试记录ID
     * @param question 题目
     * @param userAnswer 用户答案
     * @return 考试详情对象
     */
    public ExamDetail createExamDetail(Integer recordId, Question question, Question.Answer userAnswer) {
        ExamDetail detail = new ExamDetail();
        detail.setRecordId(recordId);
        detail.setQuestion(question);
        detail.setQuestionId(question.getQuestionId());
        detail.setUserAnswer(userAnswer);
        detail.setCorrectAnswer(question.getCorrectAnswer());
        detail.setCorrect(userAnswer != null && userAnswer.equals(question.getCorrectAnswer()));
        
        return detail;
    }
    
    /**
     * 验证考试记录
     * @param examRecord 考试记录
     * @return 验证错误信息，如果验证通过返回null
     */
    private String validateExamRecord(ExamRecord examRecord) {
        if (examRecord == null) {
            return "考试记录不能为空";
        }
        
        if (examRecord.getUserId() == null) {
            return "用户ID不能为空";
        }
        
        if (examRecord.getUserName() == null || examRecord.getUserName().trim().isEmpty()) {
            return "用户姓名不能为空";
        }
        
        if (examRecord.getTotalQuestions() == null || examRecord.getTotalQuestions() <= 0) {
            return "总题数必须大于0";
        }
        
        if (examRecord.getCorrectAnswers() == null || examRecord.getCorrectAnswers() < 0) {
            return "正确答案数不能为负数";
        }
        
        if (examRecord.getCorrectAnswers() > examRecord.getTotalQuestions()) {
            return "正确答案数不能大于总题数";
        }
        
        if (examRecord.getScore() == null || examRecord.getScore().compareTo(BigDecimal.ZERO) < 0) {
            return "成绩不能为负数";
        }
        
        if (examRecord.getScore().compareTo(BigDecimal.valueOf(100)) > 0) {
            return "成绩不能超过100分";
        }
        
        if (examRecord.getTimeUsed() == null || examRecord.getTimeUsed() < 0) {
            return "用时不能为负数";
        }
        
        if (examRecord.getExamDuration() == null || examRecord.getExamDuration() <= 0) {
            return "考试时长必须大于0";
        }
        
        return null; // 验证通过
    }
    
    /**
     * 格式化考试时间
     * @param seconds 秒数
     * @return 格式化的时间字符串
     */
    public String formatTime(int seconds) {
        int hours = seconds / 3600;
        int minutes = (seconds % 3600) / 60;
        int secs = seconds % 60;
        
        if (hours > 0) {
            return String.format("%d:%02d:%02d", hours, minutes, secs);
        } else {
            return String.format("%02d:%02d", minutes, secs);
        }
    }
    
    /**
     * 获取成绩等级描述
     * @param score 成绩
     * @return 等级描述
     */
    public String getGradeDescription(BigDecimal score) {
        if (score == null) {
            return "未知";
        }
        
        double scoreValue = score.doubleValue();
        if (scoreValue >= 90) {
            return "优秀";
        } else if (scoreValue >= 80) {
            return "良好";
        } else if (scoreValue >= 70) {
            return "中等";
        } else if (scoreValue >= 60) {
            return "及格";
        } else {
            return "不及格";
        }
    }
    
    /**
     * 检查是否通过考试
     * @param score 成绩
     * @param passScore 及格分数
     * @return true如果通过，否则false
     */
    public boolean isExamPassed(BigDecimal score, double passScore) {
        return score != null && score.doubleValue() >= passScore;
    }
    
    /**
     * 获取考试统计信息
     * @param examRecords 考试记录列表
     * @return 统计信息字符串
     */
    public String getExamStatistics(List<ExamRecord> examRecords) {
        if (examRecords == null || examRecords.isEmpty()) {
            return "暂无考试记录";
        }
        
        int total = examRecords.size();
        int passed = 0;
        BigDecimal totalScore = BigDecimal.ZERO;
        
        for (ExamRecord record : examRecords) {
            if (record.getScore() != null) {
                totalScore = totalScore.add(record.getScore());
                if (record.getScore().doubleValue() >= 60) {
                    passed++;
                }
            }
        }
        
        BigDecimal averageScore = total > 0 ? 
            totalScore.divide(BigDecimal.valueOf(total), 2, BigDecimal.ROUND_HALF_UP) : 
            BigDecimal.ZERO;
        
        double passRate = total > 0 ? (double) passed / total * 100 : 0;
        
        return String.format("总考试次数: %d, 通过次数: %d, 通过率: %.1f%%, 平均分: %.1f", 
                           total, passed, passRate, averageScore.doubleValue());
    }
    
    /**
     * 创建错误消息
     * @param errorMessage 错误信息
     * @return 错误消息对象
     */
    private Message createErrorMessage(String errorMessage) {
        Message message = new Message();
        message.setStatus(Message.MessageStatus.ERROR);
        message.setMessage(errorMessage);
        return message;
    }
    
    /**
     * 创建成功消息
     * @param successMessage 成功信息
     * @return 成功消息对象
     */
    private Message createSuccessMessage(String successMessage) {
        Message message = new Message();
        message.setStatus(Message.MessageStatus.SUCCESS);
        message.setMessage(successMessage);
        return message;
    }
}
