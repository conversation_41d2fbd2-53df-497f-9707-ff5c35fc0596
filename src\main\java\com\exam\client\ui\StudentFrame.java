package com.exam.client.ui;

import com.exam.client.ExamClient;
import com.exam.controller.ExamController;
import com.exam.controller.UserController;
import com.exam.model.Message;
import com.exam.model.ExamRecord;
import com.exam.util.ExamConfig;
import com.exam.util.NetworkUtil;
import com.google.gson.reflect.TypeToken;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * 学生主界面
 * 实现MVC模式中的View层
 * 提供考试和成绩查看功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class StudentFrame extends JFrame {
    
    private ExamClient client;
    private UserController userController;
    private ExamController examController;
    
    // UI组件
    private JTabbedPane tabbedPane;
    private JTable scoreTable;
    private DefaultTableModel scoreTableModel;
    private JLabel statusLabel;
    private JLabel userInfoLabel;
    private JSpinner questionCountSpinner;
    private SpinnerNumberModel questionCountModel; // 添加模型引用
    private JLabel questionLimitLabel; // 添加限制提示标签
    
    /**
     * 构造函数
     * @param client 客户端对象
     */
    public StudentFrame(ExamClient client) {
        this.client = client;
        this.userController = new UserController(client);
        this.examController = new ExamController(client);
        
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        setupFrame();
        loadData();
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeComponents() {
        // 创建选项卡面板
        tabbedPane = new JTabbedPane();
        
        // 创建成绩表格
        String[] scoreColumns = {"考试时间", "总题数", "正确数", "成绩", "用时", "等级", "状态"};
        scoreTableModel = new DefaultTableModel(scoreColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        scoreTable = new JTable(scoreTableModel);
        scoreTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        // 创建考试设置组件 - 动态限制题目数量
        questionCountModel = new SpinnerNumberModel(10, 5, 20, 1); // 初始最大值20，稍后会动态更新
        questionCountSpinner = new JSpinner(questionCountModel);
        
        // 创建状态标签
        statusLabel = new JLabel("就绪");
        statusLabel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        // 创建用户信息标签
        userInfoLabel = new JLabel();
        updateUserInfo();
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // 顶部面板
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 5, 10));
        
        JLabel titleLabel = new JLabel("考试系统 - 学生端");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 18)); // 增大标题字体
        topPanel.add(titleLabel, BorderLayout.WEST);
        topPanel.add(userInfoLabel, BorderLayout.EAST);
        
        add(topPanel, BorderLayout.NORTH);
        
        // 创建考试面板
        JPanel examPanel = createExamPanel();
        tabbedPane.addTab("开始考试", examPanel);
        
        // 创建成绩面板
        JPanel scorePanel = createScorePanel();
        tabbedPane.addTab("我的成绩", scorePanel);
        
        add(tabbedPane, BorderLayout.CENTER);
        
        // 底部状态栏
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.add(statusLabel, BorderLayout.WEST);
        
        JButton logoutButton = new JButton("退出登录");
        logoutButton.addActionListener(e -> logout());
        statusPanel.add(logoutButton, BorderLayout.EAST);
        
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建考试面板
     */
    private JPanel createExamPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        // 考试设置面板
        JPanel settingsPanel = new JPanel(new GridBagLayout());
        settingsPanel.setBorder(BorderFactory.createTitledBorder("考试设置"));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        
        // 题目数量 - 增大字体
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel questionLabel = new JLabel("题目数量:");
        questionLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        settingsPanel.add(questionLabel, gbc);

        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        questionCountSpinner.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        settingsPanel.add(questionCountSpinner, gbc);

        gbc.gridx = 2;
        questionLimitLabel = new JLabel("道 (最多20道)"); // 使用实例变量，稍后会动态更新
        questionLimitLabel.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        questionLimitLabel.setForeground(Color.GRAY);
        settingsPanel.add(questionLimitLabel, gbc);

        // 考试时长 - 固定为30分钟，由管理员控制，学生不可修改
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel durationTitleLabel = new JLabel("考试时长:");
        durationTitleLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        settingsPanel.add(durationTitleLabel, gbc);

        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        int configuredDuration = ExamConfig.getInstance().getDefaultExamDurationMinutes();
        JLabel durationLabel = new JLabel(configuredDuration + " 分钟 (管理员配置)");
        durationLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        durationLabel.setForeground(Color.DARK_GRAY);
        settingsPanel.add(durationLabel, gbc);
        
        // 开始考试按钮
        JButton startExamButton = new JButton("开始考试");
        startExamButton.setFont(new Font("微软雅黑", Font.BOLD, 16)); // 增大字体
        startExamButton.setPreferredSize(new Dimension(140, 45)); // 增大按钮尺寸
        startExamButton.addActionListener(e -> startExam());

        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.gridwidth = 3;
        gbc.anchor = GridBagConstraints.CENTER;
        gbc.insets = new Insets(20, 10, 10, 10);
        settingsPanel.add(startExamButton, gbc);
        
        panel.add(settingsPanel, BorderLayout.NORTH);
        return panel;
    }
    
    /**
     * 创建成绩面板
     */
    private JPanel createScorePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        // 工具栏
        JPanel toolBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        
        JButton refreshButton = new JButton("刷新");
        refreshButton.addActionListener(e -> loadScores());
        
        toolBar.add(refreshButton);
        
        panel.add(toolBar, BorderLayout.NORTH);
        
        // 表格
        JScrollPane scrollPane = new JScrollPane(scoreTable);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 窗口关闭事件
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        addWindowListener(new java.awt.event.WindowAdapter() {
            @Override
            public void windowClosing(java.awt.event.WindowEvent e) {
                logout();
            }
        });
    }
    
    /**
     * 设置窗口属性
     */
    private void setupFrame() {
        setTitle("考试系统 - 学生");
        setSize(800, 600);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
    }
    
    /**
     * 加载数据
     */
    private void loadData() {
        // 先加载成绩，完成后再加载题目数量，避免并发请求导致响应混乱
        loadScoresAndThenQuestionCount();
    }

    /**
     * 先加载成绩，然后加载题目数量
     */
    private void loadScoresAndThenQuestionCount() {
        SwingWorker<Message, Void> worker = new SwingWorker<Message, Void>() {
            @Override
            protected Message doInBackground() throws Exception {
                return examController.getScoreList();
            }

            @Override
            protected void done() {
                try {
                    Message response = get();

                    if (response.isSuccess()) {
                        // 检查响应类型是否正确
                        if (response.getType() != Message.MessageType.SCORE_LIST_RESPONSE) {
                            setStatus("收到错误的响应类型，可能是网络通信问题");

                            // 延迟后重试
                            SwingUtilities.invokeLater(() -> {
                                try {
                                    Thread.sleep(1000); // 延迟1秒
                                    loadScoresAndThenQuestionCount(); // 重试
                                } catch (InterruptedException e) {
                                    Thread.currentThread().interrupt();
                                }
                            });
                            return;
                        }

                        // 使用安全的类型转换方法
                        TypeToken<List<ExamRecord>> typeToken = new TypeToken<List<ExamRecord>>() {};
                        List<ExamRecord> records = NetworkUtil.extractDataSafely(response.getData(), typeToken);

                        if (records != null) {
                            updateScoreTable(records);
                            setStatus("成绩列表加载完成，共" + records.size() + "条记录");
                        } else {
                            setStatus("成绩数据格式错误");
                            JOptionPane.showMessageDialog(StudentFrame.this,
                                "成绩数据格式错误，请检查服务器配置",
                                "数据错误", JOptionPane.ERROR_MESSAGE);
                        }
                    } else {
                        setStatus("加载成绩失败: " + response.getMessage());
                        JOptionPane.showMessageDialog(StudentFrame.this,
                            "加载成绩失败: " + response.getMessage(),
                            "加载失败", JOptionPane.ERROR_MESSAGE);
                    }

                    // 成绩加载完成后，再加载题目数量
                    SwingUtilities.invokeLater(() -> {
                        try {
                            Thread.sleep(500); // 延迟500ms确保网络通信完成
                            loadAvailableQuestionCount();
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    });

                } catch (Exception e) {
                    setStatus("加载成绩异常: " + e.getMessage());
                    e.printStackTrace();
                    JOptionPane.showMessageDialog(StudentFrame.this,
                        "加载成绩异常: " + e.getMessage(),
                        "系统异常", JOptionPane.ERROR_MESSAGE);

                    // 即使成绩加载失败，也要尝试加载题目数量
                    SwingUtilities.invokeLater(() -> {
                        try {
                            Thread.sleep(500);
                            loadAvailableQuestionCount();
                        } catch (InterruptedException ex) {
                            Thread.currentThread().interrupt();
                        }
                    });
                }
            }
        };
        worker.execute();
    }
    
    /**
     * 开始考试
     */
    private void startExam() {
        int questionCount = (Integer) questionCountSpinner.getValue();
        int examDurationMinutes = ExamConfig.getInstance().getDefaultExamDurationMinutes(); // 从配置获取考试时长
        int examDurationSeconds = examDurationMinutes * 60;
        
        // 确认开始考试
        int confirm = JOptionPane.showConfirmDialog(this,
            String.format("确定开始考试吗？\n题目数量：%d道\n考试时长：%d分钟", 
                         questionCount, examDurationMinutes),
            "确认开始考试",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.QUESTION_MESSAGE);
        
        if (confirm == JOptionPane.YES_OPTION) {
            // 获取考试题目
            SwingWorker<Message, Void> worker = new SwingWorker<Message, Void>() {
                @Override
                protected Message doInBackground() throws Exception {
                    return examController.startExam(questionCount, examDurationSeconds);
                }
                
                @Override
                protected void done() {
                    try {
                        Message response = get();
                        if (response.isSuccess()) {
                            // 打开考试界面
                            SwingUtilities.invokeLater(() -> {
                                try {
                                    ExamFrame examFrame = new ExamFrame(client, response, examDurationSeconds);
                                    examFrame.setVisible(true);
                                    setVisible(false);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    setStatus("创建考试界面失败: " + e.getMessage());
                                    JOptionPane.showMessageDialog(StudentFrame.this,
                                        "创建考试界面失败: " + e.getMessage(),
                                        "错误", JOptionPane.ERROR_MESSAGE);
                                }
                            });
                        } else {
                            setStatus("开始考试失败: " + response.getMessage());
                            JOptionPane.showMessageDialog(StudentFrame.this,
                                "开始考试失败: " + response.getMessage(),
                                "错误", JOptionPane.ERROR_MESSAGE);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        setStatus("开始考试异常: " + e.getMessage());
                        JOptionPane.showMessageDialog(StudentFrame.this,
                            "开始考试异常: " + e.getMessage(),
                            "系统异常", JOptionPane.ERROR_MESSAGE);
                    }
                }
            };
            worker.execute();
        }
    }
    
    /**
     * 加载成绩列表（单独调用，用于刷新按钮）
     */
    private void loadScores() {
        SwingWorker<Message, Void> worker = new SwingWorker<Message, Void>() {
            @Override
            protected Message doInBackground() throws Exception {
                return examController.getScoreList();
            }

            @Override
            protected void done() {
                try {
                    Message response = get();

                    if (response.isSuccess()) {
                        // 检查响应类型是否正确
                        if (response.getType() != Message.MessageType.SCORE_LIST_RESPONSE) {
                            setStatus("收到错误的响应类型，请稍后重试");
                            return;
                        }

                        // 使用安全的类型转换方法
                        TypeToken<List<ExamRecord>> typeToken = new TypeToken<List<ExamRecord>>() {};
                        List<ExamRecord> records = NetworkUtil.extractDataSafely(response.getData(), typeToken);

                        if (records != null) {
                            updateScoreTable(records);
                            setStatus("成绩列表刷新完成，共" + records.size() + "条记录");
                        } else {
                            setStatus("成绩数据格式错误");
                        }
                    } else {
                        setStatus("刷新成绩失败: " + response.getMessage());
                    }
                } catch (Exception e) {
                    setStatus("刷新成绩异常: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        };
        worker.execute();
    }

    /**
     * 加载可用题目数量并更新spinner限制
     */
    private void loadAvailableQuestionCount() {
        SwingWorker<Message, Void> worker = new SwingWorker<Message, Void>() {
            @Override
            protected Message doInBackground() throws Exception {
                return examController.getAvailableQuestionCount();
            }

            @Override
            protected void done() {
                try {
                    Message response = get();

                    if (response.isSuccess()) {
                        // 检查响应类型是否正确
                        if (response.getType() != Message.MessageType.QUESTION_COUNT_RESPONSE) {
                            updateQuestionCountLimit(20); // 使用默认值
                            return;
                        }

                        Object data = response.getData();
                        int availableCount = 0;

                        if (data instanceof Integer) {
                            availableCount = (Integer) data;
                        } else if (data instanceof Number) {
                            availableCount = ((Number) data).intValue();
                        } else if (data instanceof String) {
                            try {
                                availableCount = Integer.parseInt((String) data);
                            } catch (NumberFormatException e) {
                                System.err.println("题目数量格式错误: " + data);
                                availableCount = 20; // 默认值
                            }
                        } else if (data instanceof List) {
                            // 如果收到的是题目列表，计算列表大小
                            availableCount = ((List<?>) data).size();
                            System.out.println("收到题目列表，计算数量: " + availableCount);
                        } else {
                            System.err.println("题目数量数据类型错误: " + (data != null ? data.getClass().getName() : "null"));
                            System.err.println("数据内容: " + data);
                            availableCount = 20; // 默认值
                        }

                        // 更新spinner的最大值
                        updateQuestionCountLimit(availableCount);

                    } else {
                        System.err.println("获取题目数量失败: " + response.getMessage());
                        // 使用默认值
                        updateQuestionCountLimit(20);
                    }
                } catch (Exception e) {
                    System.err.println("加载题目数量异常: " + e.getMessage());
                    e.printStackTrace();
                    // 使用默认值
                    updateQuestionCountLimit(20);
                }
            }
        };
        worker.execute();
    }

    /**
     * 更新题目数量限制
     */
    private void updateQuestionCountLimit(int maxCount) {
        SwingUtilities.invokeLater(() -> {
            try {
                // 确保最大值至少为5，但不超过实际可用数量
                int actualMax = Math.max(5, maxCount);

                // 获取当前值
                int currentValue = (Integer) questionCountModel.getValue();

                // 更新模型的最大值
                questionCountModel.setMaximum(actualMax);

                // 如果当前值超过新的最大值，调整当前值
                if (currentValue > actualMax) {
                    questionCountModel.setValue(actualMax);
                }

                // 更新显示标签
                if (questionLimitLabel != null) {
                    String labelText = String.format("道 (最多%d道)", actualMax);
                    questionLimitLabel.setText(labelText);
                    questionLimitLabel.repaint();
                }

                questionCountSpinner.repaint();
            } catch (Exception e) {
                System.err.println("更新题目数量限制失败: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    /**
     * 更新成绩表格
     */
    private void updateScoreTable(List<ExamRecord> records) {
        scoreTableModel.setRowCount(0);

        if (records == null || records.isEmpty()) {
            System.out.println("成绩记录为空");
            return;
        }

        for (ExamRecord record : records) {
            if (record == null) {
                System.err.println("发现空的成绩记录，跳过");
                continue;
            }

            try {
                Object[] row = {
                    record.getExamTime() != null ? record.getExamTime() : "未知时间",
                    record.getTotalQuestions() != null ? record.getTotalQuestions() : 0,
                    record.getCorrectAnswers() != null ? record.getCorrectAnswers() : 0,
                    (record.getScore() != null ? record.getScore() : 0) + "分",
                    record.getTimeUsed() != null ? examController.formatTime(record.getTimeUsed()) : "未知",
                    record.getGradeLevel() != null ? record.getGradeLevel() : "未评级",
                    record.getStatus() != null ? record.getStatus().getDescription() : "未知状态"
                };
                scoreTableModel.addRow(row);
            } catch (Exception e) {
                System.err.println("添加成绩记录到表格失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 退出登录
     */
    private void logout() {
        int option = JOptionPane.showConfirmDialog(this,
            "确定要退出登录吗？", "确认退出",
            JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
        
        if (option == JOptionPane.YES_OPTION) {
            userController.logout();
            client.disconnect();
            
            SwingUtilities.invokeLater(() -> {
                LoginFrame loginFrame = new LoginFrame(client);
                loginFrame.setVisible(true);
                dispose();
            });
        }
    }
    
    /**
     * 更新用户信息
     */
    private void updateUserInfo() {
        if (userInfoLabel != null) {
            userInfoLabel.setText(userController.getUserDisplayName());
        }
    }
    
    /**
     * 设置状态信息
     */
    private void setStatus(String status) {
        if (statusLabel != null) {
            statusLabel.setText(status);
        }
    }
}
