package com.exam.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 网络通信消息实体类
 * 用于客户端和服务器之间的数据传输
 * 实现MVC模式中的Model层
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class Message implements Serializable {
    private static final long serialVersionUID = 1L;
    
    // 消息类型枚举
    public enum MessageType {
        // 用户相关
        LOGIN_REQUEST("LOGIN_REQUEST", "登录请求"),
        LOGIN_RESPONSE("LOGIN_RESPONSE", "登录响应"),
        REGISTER_REQUEST("REGISTER_REQUEST", "注册请求"),
        REGISTER_RESPONSE("REGISTER_RESPONSE", "注册响应"),
        LOGOUT_REQUEST("LOGOUT_REQUEST", "登出请求"),
        
        // 题目相关
        QUESTION_LIST_REQUEST("QUESTION_LIST_REQUEST", "题目列表请求"),
        QUESTION_LIST_RESPONSE("QUESTION_LIST_RESPONSE", "题目列表响应"),
        QUESTION_COUNT_REQUEST("QUESTION_COUNT_REQUEST", "题目数量请求"),
        QUESTION_COUNT_RESPONSE("QUESTION_COUNT_RESPONSE", "题目数量响应"),
        QUESTION_ADD_REQUEST("QUESTION_ADD_REQUEST", "添加题目请求"),
        QUESTION_UPDATE_REQUEST("QUESTION_UPDATE_REQUEST", "更新题目请求"),
        QUESTION_DELETE_REQUEST("QUESTION_DELETE_REQUEST", "删除题目请求"),
        QUESTION_OPERATION_RESPONSE("QUESTION_OPERATION_RESPONSE", "题目操作响应"),
        
        // 考试相关
        EXAM_START_REQUEST("EXAM_START_REQUEST", "开始考试请求"),
        EXAM_START_RESPONSE("EXAM_START_RESPONSE", "开始考试响应"),
        EXAM_SUBMIT_REQUEST("EXAM_SUBMIT_REQUEST", "提交考试请求"),
        EXAM_SUBMIT_RESPONSE("EXAM_SUBMIT_RESPONSE", "提交考试响应"),
        EXAM_QUESTIONS_REQUEST("EXAM_QUESTIONS_REQUEST", "获取考试题目请求"),
        EXAM_QUESTIONS_RESPONSE("EXAM_QUESTIONS_RESPONSE", "获取考试题目响应"),
        
        // 成绩相关
        SCORE_LIST_REQUEST("SCORE_LIST_REQUEST", "成绩列表请求"),
        SCORE_LIST_RESPONSE("SCORE_LIST_RESPONSE", "成绩列表响应"),
        SCORE_DETAIL_REQUEST("SCORE_DETAIL_REQUEST", "成绩详情请求"),
        SCORE_DETAIL_RESPONSE("SCORE_DETAIL_RESPONSE", "成绩详情响应"),
        
        // 系统相关
        HEARTBEAT("HEARTBEAT", "心跳包"),
        ERROR_RESPONSE("ERROR_RESPONSE", "错误响应"),
        SUCCESS_RESPONSE("SUCCESS_RESPONSE", "成功响应");
        
        private final String code;
        private final String description;
        
        MessageType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() { return code; }
        public String getDescription() { return description; }
    }
    
    // 消息状态枚举
    public enum MessageStatus {
        SUCCESS("SUCCESS", "成功"),
        FAILED("FAILED", "失败"),
        ERROR("ERROR", "错误"),
        PENDING("PENDING", "处理中");
        
        private final String code;
        private final String description;
        
        MessageStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() { return code; }
        public String getDescription() { return description; }
    }
    
    // 消息属性
    private String messageId;           // 消息ID
    private MessageType type;           // 消息类型
    private MessageStatus status;       // 消息状态
    private String message;             // 消息内容
    private Object data;                // 数据载荷
    private Map<String, Object> params; // 参数映射
    private long timestamp;             // 时间戳
    private String sessionId;           // 会话ID
    private Integer userId;             // 用户ID
    
    // 默认构造函数
    public Message() {
        this.messageId = generateMessageId();
        this.timestamp = System.currentTimeMillis();
        this.params = new HashMap<>();
        this.status = MessageStatus.PENDING;
    }
    
    // 带类型的构造函数
    public Message(MessageType type) {
        this();
        this.type = type;
    }
    
    // 带类型和数据的构造函数
    public Message(MessageType type, Object data) {
        this(type);
        this.data = data;
    }
    
    // 带类型、数据和消息的构造函数
    public Message(MessageType type, Object data, String message) {
        this(type, data);
        this.message = message;
    }
    
    // Getter和Setter方法
    public String getMessageId() {
        return messageId;
    }
    
    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }
    
    public MessageType getType() {
        return type;
    }
    
    public void setType(MessageType type) {
        this.type = type;
    }
    
    public MessageStatus getStatus() {
        return status;
    }
    
    public void setStatus(MessageStatus status) {
        this.status = status;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public Object getData() {
        return data;
    }
    
    public void setData(Object data) {
        this.data = data;
    }
    
    public Map<String, Object> getParams() {
        return params;
    }
    
    public void setParams(Map<String, Object> params) {
        this.params = params;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public Integer getUserId() {
        return userId;
    }
    
    public void setUserId(Integer userId) {
        this.userId = userId;
    }
    
    // 业务方法
    
    /**
     * 添加参数
     * @param key 参数键
     * @param value 参数值
     * @return 当前消息对象（支持链式调用）
     */
    public Message addParam(String key, Object value) {
        if (params == null) {
            params = new HashMap<>();
        }
        params.put(key, value);
        return this;
    }
    
    /**
     * 获取参数
     * @param key 参数键
     * @return 参数值
     */
    public Object getParam(String key) {
        return params != null ? params.get(key) : null;
    }
    
    /**
     * 获取字符串类型参数
     * @param key 参数键
     * @return 字符串参数值
     */
    public String getStringParam(String key) {
        Object value = getParam(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * 获取整数类型参数
     * @param key 参数键
     * @return 整数参数值
     */
    public Integer getIntParam(String key) {
        Object value = getParam(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    /**
     * 创建成功响应消息
     * @param requestType 请求类型
     * @param data 响应数据
     * @return 响应消息
     */
    public static Message createSuccessResponse(MessageType requestType, Object data) {
        MessageType responseType = getResponseType(requestType);
        Message response = new Message(responseType, data);
        response.setStatus(MessageStatus.SUCCESS);
        response.setMessage("操作成功");
        return response;
    }
    
    /**
     * 创建错误响应消息
     * @param requestType 请求类型
     * @param errorMessage 错误消息
     * @return 错误响应消息
     */
    public static Message createErrorResponse(MessageType requestType, String errorMessage) {
        MessageType responseType = getResponseType(requestType);
        Message response = new Message(responseType);
        response.setStatus(MessageStatus.ERROR);
        response.setMessage(errorMessage);
        return response;
    }
    
    /**
     * 根据请求类型获取对应的响应类型
     * @param requestType 请求类型
     * @return 响应类型
     */
    private static MessageType getResponseType(MessageType requestType) {
        switch (requestType) {
            case LOGIN_REQUEST: return MessageType.LOGIN_RESPONSE;
            case REGISTER_REQUEST: return MessageType.REGISTER_RESPONSE;
            case QUESTION_LIST_REQUEST: return MessageType.QUESTION_LIST_RESPONSE;
            case QUESTION_COUNT_REQUEST: return MessageType.QUESTION_COUNT_RESPONSE;
            case QUESTION_ADD_REQUEST:
            case QUESTION_UPDATE_REQUEST:
            case QUESTION_DELETE_REQUEST: return MessageType.QUESTION_OPERATION_RESPONSE;
            case EXAM_START_REQUEST: return MessageType.EXAM_START_RESPONSE;
            case EXAM_SUBMIT_REQUEST: return MessageType.EXAM_SUBMIT_RESPONSE;
            case EXAM_QUESTIONS_REQUEST: return MessageType.EXAM_QUESTIONS_RESPONSE;
            case SCORE_LIST_REQUEST: return MessageType.SCORE_LIST_RESPONSE;
            case SCORE_DETAIL_REQUEST: return MessageType.SCORE_DETAIL_RESPONSE;
            default: return MessageType.SUCCESS_RESPONSE;
        }
    }
    
    /**
     * 生成消息ID
     * @return 消息ID
     */
    private String generateMessageId() {
        return "MSG_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }
    
    /**
     * 检查是否为成功状态
     * @return true如果成功，否则false
     */
    public boolean isSuccess() {
        return MessageStatus.SUCCESS.equals(status);
    }
    
    /**
     * 检查是否为错误状态
     * @return true如果错误，否则false
     */
    public boolean isError() {
        return MessageStatus.ERROR.equals(status) || MessageStatus.FAILED.equals(status);
    }
    
    @Override
    public String toString() {
        return "Message{" +
                "messageId='" + messageId + '\'' +
                ", type=" + type +
                ", status=" + status +
                ", message='" + message + '\'' +
                ", timestamp=" + timestamp +
                ", userId=" + userId +
                '}';
    }
}
