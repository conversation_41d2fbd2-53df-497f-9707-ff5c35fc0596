package com.exam.controller;

import com.exam.client.ExamClient;
import com.exam.model.Message;
import com.exam.model.User;

import java.io.IOException;

/**
 * 用户控制器
 * 实现MVC模式中的Controller层
 * 处理用户相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class UserController {
    
    private ExamClient client;
    
    /**
     * 构造函数
     * @param client 客户端对象
     */
    public UserController(ExamClient client) {
        this.client = client;
    }
    
    /**
     * 用户登录
     * @param username 用户名
     * @param password 密码
     * @return 登录结果消息
     */
    public Message login(String username, String password) {
        // 验证输入参数
        if (username == null || username.trim().isEmpty()) {
            return createErrorMessage("用户名不能为空");
        }
        
        if (password == null || password.trim().isEmpty()) {
            return createErrorMessage("密码不能为空");
        }
        
        // 调用客户端登录方法
        return client.login(username.trim(), password);
    }
    
    /**
     * 用户注册
     * @param user 用户信息
     * @return 注册结果消息
     */
    public Message register(User user) {
        // 验证用户信息
        String validationError = validateUser(user);
        if (validationError != null) {
            return createErrorMessage(validationError);
        }
        
        // 调用客户端注册方法
        return client.register(user);
    }
    
    /**
     * 用户登出
     * @return 登出结果消息
     */
    public Message logout() {
        try {
            Message logoutRequest = new Message(Message.MessageType.LOGOUT_REQUEST);
            return client.sendMessage(logoutRequest);
        } catch (IOException e) {
            return createErrorMessage("登出失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前登录用户
     * @return 当前用户对象
     */
    public User getCurrentUser() {
        return client.getCurrentUser();
    }
    
    /**
     * 检查用户是否已登录
     * @return true如果已登录，否则false
     */
    public boolean isLoggedIn() {
        return client.isLoggedIn();
    }
    
    /**
     * 检查当前用户是否为管理员
     * @return true如果是管理员，否则false
     */
    public boolean isAdmin() {
        User user = getCurrentUser();
        return user != null && user.isAdmin();
    }
    
    /**
     * 检查当前用户是否为学生
     * @return true如果是学生，否则false
     */
    public boolean isStudent() {
        User user = getCurrentUser();
        return user != null && user.isStudent();
    }
    
    /**
     * 验证用户信息
     * @param user 用户对象
     * @return 验证错误信息，如果验证通过返回null
     */
    private String validateUser(User user) {
        if (user == null) {
            return "用户信息不能为空";
        }
        
        // 验证用户名
        if (user.getUsername() == null || user.getUsername().trim().isEmpty()) {
            return "用户名不能为空";
        }
        
        if (user.getUsername().length() < 3) {
            return "用户名至少需要3个字符";
        }
        
        if (user.getUsername().length() > 50) {
            return "用户名不能超过50个字符";
        }
        
        // 检查用户名格式（只允许字母、数字、下划线）
        if (!user.getUsername().matches("^[a-zA-Z0-9_]+$")) {
            return "用户名只能包含字母、数字和下划线";
        }
        
        // 验证密码
        if (user.getPassword() == null || user.getPassword().trim().isEmpty()) {
            return "密码不能为空";
        }
        
        if (user.getPassword().length() < 6) {
            return "密码至少需要6个字符";
        }
        
        if (user.getPassword().length() > 100) {
            return "密码不能超过100个字符";
        }
        
        // 验证真实姓名
        if (user.getRealName() == null || user.getRealName().trim().isEmpty()) {
            return "真实姓名不能为空";
        }
        
        if (user.getRealName().length() > 50) {
            return "真实姓名不能超过50个字符";
        }
        
        // 验证邮箱格式（如果提供）
        if (user.getEmail() != null && !user.getEmail().trim().isEmpty()) {
            if (!isValidEmail(user.getEmail())) {
                return "邮箱格式不正确";
            }
        }
        
        // 验证电话格式（如果提供）
        if (user.getPhone() != null && !user.getPhone().trim().isEmpty()) {
            if (!isValidPhone(user.getPhone())) {
                return "电话格式不正确";
            }
        }
        
        // 验证用户类型
        if (user.getUserType() == null) {
            return "用户类型不能为空";
        }
        
        return null; // 验证通过
    }
    
    /**
     * 验证邮箱格式
     * @param email 邮箱地址
     * @return true如果格式正确，否则false
     */
    private boolean isValidEmail(String email) {
        String emailRegex = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@" +
                           "(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
        return email.matches(emailRegex);
    }
    
    /**
     * 验证电话格式
     * @param phone 电话号码
     * @return true如果格式正确，否则false
     */
    private boolean isValidPhone(String phone) {
        // 简单的电话号码验证（支持手机号和固话）
        String phoneRegex = "^(\\+?86)?[1-9]\\d{6,11}$";
        return phone.matches(phoneRegex);
    }
    
    /**
     * 创建错误消息
     * @param errorMessage 错误信息
     * @return 错误消息对象
     */
    private Message createErrorMessage(String errorMessage) {
        Message message = new Message();
        message.setStatus(Message.MessageStatus.ERROR);
        message.setMessage(errorMessage);
        return message;
    }
    
    /**
     * 创建成功消息
     * @param successMessage 成功信息
     * @return 成功消息对象
     */
    private Message createSuccessMessage(String successMessage) {
        Message message = new Message();
        message.setStatus(Message.MessageStatus.SUCCESS);
        message.setMessage(successMessage);
        return message;
    }
    
    /**
     * 获取用户显示名称
     * @return 用户显示名称
     */
    public String getUserDisplayName() {
        User user = getCurrentUser();
        if (user != null) {
            return user.getRealName() + " (" + user.getUserType().getDescription() + ")";
        }
        return "未登录";
    }

    /**
     * 获取学生身份信息显示文本
     * @return 格式化的学生身份信息，如果不是学生则返回用户显示名称
     */
    public String getStudentIdentificationDisplay() {
        User user = getCurrentUser();
        if (user != null) {
            if (user.isStudent()) {
                // 对于学生用户，显示固定的学生信息
                return "学号: 3240608005 姓名: 黄雁";
            } else {
                // 对于管理员用户，显示常规信息
                return user.getRealName() + " (" + user.getUserType().getDescription() + ")";
            }
        }
        return "未登录";
    }

    /**
     * 获取简化的学生身份信息
     * @return 简化的学生身份信息
     */
    public String getSimpleStudentInfo() {
        User user = getCurrentUser();
        if (user != null && user.isStudent()) {
            return "3240608005 - 黄雁";
        }
        return getUserDisplayName();
    }
    
    /**
     * 获取用户权限描述
     * @return 权限描述字符串
     */
    public String getUserPermissionDescription() {
        User user = getCurrentUser();
        if (user == null) {
            return "无权限";
        }
        
        if (user.isAdmin()) {
            return "管理员权限：可以管理题目、查看所有成绩";
        } else if (user.isStudent()) {
            return "学生权限：可以参加考试、查看个人成绩";
        } else {
            return "未知权限";
        }
    }
    
    /**
     * 检查用户是否有指定权限
     * @param permission 权限类型
     * @return true如果有权限，否则false
     */
    public boolean hasPermission(UserPermission permission) {
        User user = getCurrentUser();
        if (user == null) {
            return false;
        }
        
        switch (permission) {
            case MANAGE_QUESTIONS:
            case VIEW_ALL_SCORES:
            case MANAGE_USERS:
                return user.isAdmin();
                
            case TAKE_EXAM:
            case VIEW_OWN_SCORES:
                return user.isStudent() || user.isAdmin();
                
            default:
                return false;
        }
    }
    
    /**
     * 用户权限枚举
     */
    public enum UserPermission {
        MANAGE_QUESTIONS,    // 管理题目
        VIEW_ALL_SCORES,     // 查看所有成绩
        MANAGE_USERS,        // 管理用户
        TAKE_EXAM,           // 参加考试
        VIEW_OWN_SCORES      // 查看个人成绩
    }
}
