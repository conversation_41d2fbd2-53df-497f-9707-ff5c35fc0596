package com.exam.util;

import com.exam.model.Message;
import com.exam.model.Question;
import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;

import java.io.*;
import java.net.Socket;
import java.nio.charset.StandardCharsets;

/**
 * 网络通信工具类
 * 提供客户端和服务器之间的消息传输功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class NetworkUtil {
    
    // JSON序列化工具
    private static final Gson gson = new GsonBuilder()
            .setDateFormat("yyyy-MM-dd HH:mm:ss")
            .setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE)
            .setNumberToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE)
            .create();
    
    // 消息分隔符
    private static final String MESSAGE_DELIMITER = "\n";
    
    /**
     * 发送消息到指定的输出流
     * @param message 要发送的消息对象
     * @param outputStream 输出流
     * @throws IOException 网络IO异常
     */
    public static void sendMessage(Message message, OutputStream outputStream) throws IOException {
        if (message == null || outputStream == null) {
            throw new IllegalArgumentException("消息或输出流不能为空");
        }
        
        try {
            // 将消息对象序列化为JSON字符串
            String jsonMessage = gson.toJson(message);
            
            // 添加消息分隔符
            String messageWithDelimiter = jsonMessage + MESSAGE_DELIMITER;
            
            // 转换为字节数组并发送
            byte[] messageBytes = messageWithDelimiter.getBytes(StandardCharsets.UTF_8);
            outputStream.write(messageBytes);
            outputStream.flush();
            

        } catch (Exception e) {
            System.err.println("发送消息失败: " + e.getMessage());
            throw new IOException("发送消息失败", e);
        }
    }
    
    /**
     * 从指定的输入流接收消息
     * @param inputStream 输入流
     * @return 接收到的消息对象
     * @throws IOException 网络IO异常
     */
    public static Message receiveMessage(InputStream inputStream) throws IOException {
        if (inputStream == null) {
            throw new IllegalArgumentException("输入流不能为空");
        }
        
        try {
            // 使用BufferedReader读取消息
            BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            
            // 读取一行消息（以换行符分隔）
            String jsonMessage = reader.readLine();
            
            if (jsonMessage == null || jsonMessage.trim().isEmpty()) {
                throw new IOException("接收到空消息或连接已关闭");
            }
            
            // 将JSON字符串反序列化为消息对象
            return gson.fromJson(jsonMessage, Message.class);
        } catch (Exception e) {
            System.err.println("接收消息失败: " + e.getMessage());
            throw new IOException("接收消息失败", e);
        }
    }
    
    /**
     * 通过Socket发送消息
     * @param message 要发送的消息
     * @param socket Socket连接
     * @throws IOException 网络IO异常
     */
    public static void sendMessage(Message message, Socket socket) throws IOException {
        if (socket == null || socket.isClosed()) {
            throw new IOException("Socket连接无效");
        }
        
        sendMessage(message, socket.getOutputStream());
    }
    
    /**
     * 通过Socket接收消息
     * @param socket Socket连接
     * @return 接收到的消息对象
     * @throws IOException 网络IO异常
     */
    public static Message receiveMessage(Socket socket) throws IOException {
        if (socket == null || socket.isClosed()) {
            throw new IOException("Socket连接无效");
        }
        
        return receiveMessage(socket.getInputStream());
    }
    
    /**
     * 发送请求并等待响应
     * @param request 请求消息
     * @param socket Socket连接
     * @return 响应消息
     * @throws IOException 网络IO异常
     */
    public static Message sendRequestAndWaitResponse(Message request, Socket socket) throws IOException {
        // 发送请求
        sendMessage(request, socket);

        // 等待响应，并验证响应类型
        Message response = receiveMessage(socket);

        // 验证响应类型是否匹配请求类型
        Message.MessageType expectedResponseType = getExpectedResponseType(request.getType());
        if (response.getType() != expectedResponseType) {
            System.err.println("NetworkUtil: 响应类型不匹配！请求类型: " + request.getType() +
                             ", 期望响应类型: " + expectedResponseType +
                             ", 实际响应类型: " + response.getType());
            // 可以选择抛出异常或者返回错误响应
            // 这里我们记录错误但仍然返回响应，让上层处理
        }

        return response;
    }

    /**
     * 根据请求类型获取期望的响应类型
     * @param requestType 请求类型
     * @return 期望的响应类型
     */
    private static Message.MessageType getExpectedResponseType(Message.MessageType requestType) {
        switch (requestType) {
            case LOGIN_REQUEST: return Message.MessageType.LOGIN_RESPONSE;
            case REGISTER_REQUEST: return Message.MessageType.REGISTER_RESPONSE;
            case QUESTION_LIST_REQUEST: return Message.MessageType.QUESTION_LIST_RESPONSE;
            case QUESTION_COUNT_REQUEST: return Message.MessageType.QUESTION_COUNT_RESPONSE;
            case QUESTION_ADD_REQUEST:
            case QUESTION_UPDATE_REQUEST:
            case QUESTION_DELETE_REQUEST: return Message.MessageType.QUESTION_OPERATION_RESPONSE;
            case EXAM_START_REQUEST: return Message.MessageType.EXAM_START_RESPONSE;
            case EXAM_SUBMIT_REQUEST: return Message.MessageType.EXAM_SUBMIT_RESPONSE;
            case SCORE_LIST_REQUEST: return Message.MessageType.SCORE_LIST_RESPONSE;
            case SCORE_DETAIL_REQUEST: return Message.MessageType.SCORE_DETAIL_RESPONSE;
            case HEARTBEAT: return Message.MessageType.HEARTBEAT;
            default: return Message.MessageType.SUCCESS_RESPONSE;
        }
    }
    
    /**
     * 创建心跳消息
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @return 心跳消息
     */
    public static Message createHeartbeatMessage(Integer userId, String sessionId) {
        Message heartbeat = new Message(Message.MessageType.HEARTBEAT);
        heartbeat.setUserId(userId);
        heartbeat.setSessionId(sessionId);
        heartbeat.setMessage("心跳包");
        return heartbeat;
    }
    
    /**
     * 创建错误响应消息
     * @param originalMessage 原始消息
     * @param errorMessage 错误信息
     * @return 错误响应消息
     */
    public static Message createErrorResponse(Message originalMessage, String errorMessage) {
        Message errorResponse = new Message(Message.MessageType.ERROR_RESPONSE);
        errorResponse.setStatus(Message.MessageStatus.ERROR);
        errorResponse.setMessage(errorMessage);
        
        if (originalMessage != null) {
            errorResponse.setSessionId(originalMessage.getSessionId());
            errorResponse.setUserId(originalMessage.getUserId());
        }
        
        return errorResponse;
    }
    
    /**
     * 创建成功响应消息
     * @param originalMessage 原始消息
     * @param data 响应数据
     * @return 成功响应消息
     */
    public static Message createSuccessResponse(Message originalMessage, Object data) {
        Message successResponse = new Message(Message.MessageType.SUCCESS_RESPONSE);
        successResponse.setStatus(Message.MessageStatus.SUCCESS);
        successResponse.setMessage("操作成功");
        successResponse.setData(data);
        
        if (originalMessage != null) {
            successResponse.setSessionId(originalMessage.getSessionId());
            successResponse.setUserId(originalMessage.getUserId());
        }
        
        return successResponse;
    }
    
    /**
     * 检查Socket连接是否有效
     * @param socket Socket连接
     * @return true如果连接有效，否则false
     */
    public static boolean isSocketValid(Socket socket) {
        return socket != null && 
               !socket.isClosed() && 
               socket.isConnected() && 
               !socket.isInputShutdown() && 
               !socket.isOutputShutdown();
    }
    
    /**
     * 安全关闭Socket连接
     * @param socket Socket连接
     */
    public static void closeSocket(Socket socket) {
        if (socket != null && !socket.isClosed()) {
            try {
                // 关闭输入输出流
                if (!socket.isInputShutdown()) {
                    socket.shutdownInput();
                }
                if (!socket.isOutputShutdown()) {
                    socket.shutdownOutput();
                }
                // 关闭Socket
                socket.close();
                System.out.println("Socket连接已关闭");
            } catch (IOException e) {
                System.err.println("关闭Socket失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 生成会话ID
     * @return 会话ID字符串
     */
    public static String generateSessionId() {
        return "SESSION_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 10000);
    }
    
    /**
     * 将对象序列化为JSON字符串
     * @param object 要序列化的对象
     * @return JSON字符串
     */
    public static String toJson(Object object) {
        return gson.toJson(object);
    }
    
    /**
     * 将JSON字符串反序列化为指定类型的对象
     * @param json JSON字符串
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 反序列化后的对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        return gson.fromJson(json, clazz);
    }

    /**
     * 将JSON字符串反序列化为指定泛型类型的对象
     * @param json JSON字符串
     * @param type 泛型类型
     * @param <T> 泛型类型
     * @return 反序列化后的对象
     */
    public static <T> T fromJson(String json, Type type) {
        return gson.fromJson(json, type);
    }

    /**
     * 将JSON字符串反序列化为指定TypeToken类型的对象
     * @param json JSON字符串
     * @param typeToken TypeToken对象
     * @param <T> 泛型类型
     * @return 反序列化后的对象
     */
    public static <T> T fromJson(String json, TypeToken<T> typeToken) {
        return gson.fromJson(json, typeToken.getType());
    }

    /**
     * 安全地从Message数据中提取指定类型的对象
     * 处理Gson反序列化时LinkedTreeMap的问题
     * @param data Message中的数据对象
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 转换后的对象，如果转换失败返回null
     */
    public static <T> T extractDataSafely(Object data, Class<T> clazz) {
        if (data == null) {
            return null;
        }

        try {
            // 如果数据已经是目标类型，直接返回
            if (clazz.isInstance(data)) {
                return clazz.cast(data);
            }

            // 否则通过JSON重新序列化和反序列化来转换
            String json = gson.toJson(data);

            return gson.fromJson(json, clazz);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 安全地从Message数据中提取指定泛型类型的对象
     * 处理Gson反序列化时LinkedTreeMap的问题
     * @param data Message中的数据对象
     * @param type 泛型类型
     * @param <T> 泛型类型
     * @return 转换后的对象，如果转换失败返回null
     */
    public static <T> T extractDataSafely(Object data, Type type) {
        if (data == null) {
            return null;
        }

        try {
            // 通过JSON重新序列化和反序列化来转换
            String json = gson.toJson(data);
            return gson.fromJson(json, type);
        } catch (Exception e) {
            System.err.println("数据类型转换失败: " + e.getMessage());
            System.err.println("数据类型: " + data.getClass().getName());
            System.err.println("数据内容: " + data);
            System.err.println("目标类型: " + type.getTypeName());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 安全地从Message数据中提取指定TypeToken类型的对象
     * 处理Gson反序列化时LinkedTreeMap的问题
     * @param data Message中的数据对象
     * @param typeToken TypeToken对象
     * @param <T> 泛型类型
     * @return 转换后的对象，如果转换失败返回null
     */
    public static <T> T extractDataSafely(Object data, TypeToken<T> typeToken) {
        return extractDataSafely(data, typeToken.getType());
    }
    
    /**
     * 获取本地IP地址（简单实现）
     * @return IP地址字符串
     */
    public static String getLocalIPAddress() {
        try {
            return java.net.InetAddress.getLocalHost().getHostAddress();
        } catch (Exception e) {
            return "127.0.0.1";
        }
    }
    
    /**
     * 检查端口是否可用
     * @param port 端口号
     * @return true如果端口可用，否则false
     */
    public static boolean isPortAvailable(int port) {
        try (java.net.ServerSocket serverSocket = new java.net.ServerSocket(port)) {
            return true;
        } catch (IOException e) {
            return false;
        }
    }
}
