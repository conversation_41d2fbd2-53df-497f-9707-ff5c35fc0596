# 网上考试系统

## 项目简介

这是一个基于C/S架构的网上考试系统，使用Java 8 + Swing + MySQL 8技术栈开发。系统支持多用户并发访问，实现了完整的在线考试功能。

## 技术特点

- **架构模式**：C/S架构 + MVC设计模式
- **技术栈**：Java 8 + Swing + MySQL 8 + Socket + Maven
- **设计原则**：遵循开闭原则，具备良好的可扩展性
- **并发支持**：多线程处理，支持多用户同时使用
- **网络通信**：基于Socket + JSON的消息传输机制

## 功能特性

### 用户管理
- ✅ 用户注册（学生/管理员）
- ✅ 用户登录验证
- ✅ 权限控制和会话管理

### 题库管理（管理员）
- ✅ 题目增加、编辑、删除（完整CRUD操作）
- ✅ 题目列表查看和管理
- ✅ 支持不同难度等级和科目分类
- ✅ 输入验证和错误处理

### 在线考试（学生）
- ✅ 考试参数设置（题目数量、考试时长）
- ✅ 随机抽题机制
- ✅ 实时计时和自动提交
- ✅ 友好的答题界面

### 成绩管理
- ✅ 自动评分和成绩计算
- ✅ 考试记录保存
- ✅ 成绩查询和统计
- ✅ 学生个人成绩查看
- ✅ 管理员全局成绩管理
- ✅ 修复JSON反序列化问题

## 最新修复（v1.1）

### 🔧 JDK 8兼容性修复
- ✅ 移除所有`var`关键字，使用显式类型声明
- ✅ 确保所有代码与JDK 8完全兼容
- ✅ 添加编译验证和测试

### 🔧 JSON反序列化修复
- ✅ 修复ClassCastException: LinkedTreeMap无法转换为ExamRecord
- ✅ 修复ClassCastException: LinkedTreeMap无法转换为Question
- ✅ 实现安全的类型转换方法
- ✅ 添加TypeToken支持泛型类型反序列化
- ✅ 增强错误处理和用户友好的错误消息

### 🔧 CRUD操作完善
- ✅ 实现完整的题目添加功能（QuestionDialog）
- ✅ 实现完整的题目编辑功能
- ✅ 添加输入验证和数据完整性检查
- ✅ 改进数据库操作的错误处理

### 🔧 系统稳定性提升
- ✅ 增强数据库连接错误处理
- ✅ 添加详细的日志输出和调试信息
- ✅ 实现数据库连接测试工具
- ✅ 添加JSON序列化/反序列化测试

## 系统架构

```
┌─────────────────┐    Socket/JSON    ┌─────────────────┐
│   客户端 (Client)  │ ←──────────────→ │   服务器 (Server)  │
│                 │                  │                 │
│ ┌─────────────┐ │                  │ ┌─────────────┐ │
│ │    View     │ │                  │ │ ClientHandler│ │
│ │  (Swing UI) │ │                  │ │             │ │
│ └─────────────┘ │                  │ └─────────────┘ │
│ ┌─────────────┐ │                  │ ┌─────────────┐ │
│ │ Controller  │ │                  │ │DatabaseMgr  │ │
│ │             │ │                  │ │             │ │
│ └─────────────┘ │                  │ └─────────────┘ │
│ ┌─────────────┐ │                  └─────────────────┘
│ │   Model     │ │                           │
│ │             │ │                           │
│ └─────────────┘ │                  ┌─────────────────┐
└─────────────────┘                  │  MySQL Database │
                                     │                 │
                                     │ ┌─────────────┐ │
                                     │ │    users    │ │
                                     │ │  questions  │ │
                                     │ │exam_records │ │
                                     │ │exam_details │ │
                                     │ └─────────────┘ │
                                     └─────────────────┘
```

## 环境要求

- **JDK**: 8 或以上版本
- **MySQL**: 8.0 或以上版本
- **Maven**: 3.6 或以上版本
- **操作系统**: Windows/Linux/macOS

## 快速开始

### 1. 环境准备

确保已安装JDK 8、MySQL 8和Maven。

### 2. 数据库初始化

```sql
-- 创建数据库
CREATE DATABASE exam_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 执行初始化脚本
mysql -u root -p exam_system < src/main/resources/database/init.sql
```

### 3. 配置数据库连接

编辑 `src/main/resources/config/database.properties`：

```properties
db.driver=com.mysql.cj.jdbc.Driver
db.url=*************************************************************************************************************************************
db.username=root
db.password=your_password
```

### 4. 编译项目

```bash
cd exam-system
mvn clean compile
```

### 5. 启动服务器

```bash
mvn exec:java -Dexec.mainClass="com.exam.server.ExamServer"
```

服务器默认监听端口：8888

### 6. 启动客户端

```bash
mvn exec:java -Dexec.mainClass="com.exam.client.ExamClient"
```

## 默认账户

系统预置了以下测试账户：

| 用户类型 | 用户名 | 密码 | 说明 |
|---------|--------|------|------|
| 管理员 | admin | admin123 | 系统管理员账户 |
| 学生 | student1 | 123456 | 测试学生账户1 |
| 学生 | student2 | 123456 | 测试学生账户2 |

## 使用说明

### 管理员操作流程

1. 使用管理员账户登录
2. 在"题目管理"选项卡中管理试题
   - 添加新题目
   - 编辑现有题目
   - 删除不需要的题目
3. 在"成绩管理"选项卡中查看所有学生成绩

### 学生操作流程

1. 注册新账户或使用测试账户登录
2. 在"开始考试"选项卡中设置考试参数
   - 选择题目数量（5-50题）
   - 设置考试时长（5-120分钟）
3. 点击"开始考试"进入答题界面
4. 完成答题后提交考试
5. 在"我的成绩"选项卡中查看考试结果

## 项目结构

```
exam-system/
├── src/main/java/com/exam/
│   ├── client/              # 客户端代码
│   │   ├── ExamClient.java  # 客户端主类
│   │   └── ui/              # 用户界面
│   ├── server/              # 服务器端代码
│   │   ├── ExamServer.java  # 服务器主类
│   │   ├── ClientHandler.java
│   │   └── DatabaseManager.java
│   ├── controller/          # 控制器层
│   ├── model/              # 数据模型层
│   └── util/               # 工具类
├── src/main/resources/
│   ├── database/           # 数据库脚本
│   └── config/            # 配置文件
├── pom.xml                # Maven配置
└── README.md              # 项目说明
```

## 开发说明

### MVC架构实现

- **Model层**: 实体类（User, Question, ExamRecord等）
- **View层**: Swing界面类（LoginFrame, AdminFrame等）
- **Controller层**: 业务控制类（UserController, QuestionController等）

### 扩展开发

系统遵循开闭原则，支持功能扩展：

1. **添加新题型**: 扩展Question类和相关界面
2. **增加用户角色**: 扩展UserType枚举和权限控制
3. **扩展考试功能**: 添加新的考试模式和评分规则

## 常见问题

### Q: 编译失败，提示"var"关键字错误
A: 确保使用JDK 8或以上版本。本项目已修复JDK 8兼容性问题，所有代码都使用JDK 8语法。

### Q: 出现ClassCastException错误，提示LinkedTreeMap无法转换为ExamRecord
A: 这是JSON反序列化问题，已修复。系统现在使用安全的类型转换方法处理所有JSON数据。

### Q: 无法连接数据库
A:
1. 检查MySQL服务是否启动
2. 验证数据库配置文件 `src/main/resources/config/database.properties` 中的连接信息
3. 确保数据库 `exam_system` 已创建并执行了初始化脚本
4. 运行数据库测试：`mvn exec:java -Dexec.mainClass="com.exam.test.DatabaseTest"`

### Q: 题目列表或成绩列表加载失败
A:
1. 检查数据库连接是否正常
2. 确认数据库中有示例数据（运行初始化脚本）
3. 查看服务器控制台的详细错误信息
4. 检查数据库表结构是否正确

### Q: 客户端无法连接服务器
A:
1. 确认服务器已启动并监听8888端口
2. 检查防火墙设置
3. 验证服务器IP地址和端口配置

### Q: 添加题目功能不工作
A:
1. 确保以管理员身份登录
2. 检查所有必填字段是否已填写
3. 查看服务器端的错误日志

### Q: 考试时间不准确
A: 确保客户端和服务器时间同步

## 故障排除

### 数据库问题诊断
```bash
# 1. 测试数据库连接
mvn exec:java -Dexec.mainClass="com.exam.test.DatabaseTest"

# 2. 重新初始化数据库
mysql -u root -p exam_system < src/main/resources/database/init.sql

# 3. 检查数据库表
mysql -u root -p -e "USE exam_system; SHOW TABLES; SELECT COUNT(*) FROM users; SELECT COUNT(*) FROM questions;"
```

### 编译问题诊断
```bash
# 1. 检查JDK版本
java -version
javac -version

# 2. 清理并重新编译
mvn clean
mvn compile

# 3. 检查依赖
mvn dependency:tree
```

### 网络连接问题诊断
```bash
# 1. 检查端口占用
netstat -an | findstr 8888

# 2. 测试本地连接
telnet localhost 8888
```

## 技术支持

如有问题，请查看：
1. 系统日志文件：`data/system.log`
2. 数据库连接配置：`src/main/resources/config/database.properties`
3. 技术文档：`考试系统技术报告.md`

## 许可证

本项目仅用于学习和教学目的。
