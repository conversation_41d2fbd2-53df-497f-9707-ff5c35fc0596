package com.exam.client.ui;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * 考试配置对话框
 * 用于管理员配置考试参数
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ExamConfigDialog extends JDialog {
    private static final long serialVersionUID = 1L;
    
    // UI组件
    private JSpinner examDurationSpinner;
    private JButton confirmButton;
    private JButton cancelButton;
    
    // 状态标志
    private boolean confirmed = false;
    
    // 当前配置值
    private int examDurationMinutes;
    
    /**
     * 构造函数
     * @param parent 父窗口
     * @param currentDuration 当前考试时长（分钟）
     */
    public ExamConfigDialog(JFrame parent, int currentDuration) {
        super(parent, "考试配置 - 学号: 3240608005 姓名: 黄雁", true);
        this.examDurationMinutes = currentDuration;

        initializeComponents();
        setupLayout();
        setupEventHandlers();
        setupDialog();

        // 设置当前值
        examDurationSpinner.setValue(currentDuration);
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeComponents() {
        // 考试时长输入框 - 限制在5-180分钟之间
        examDurationSpinner = new JSpinner(new SpinnerNumberModel(30, 5, 180, 5));
        examDurationSpinner.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        
        // 按钮
        confirmButton = new JButton("确定");
        confirmButton.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        confirmButton.setPreferredSize(new Dimension(80, 30));
        
        cancelButton = new JButton("取消");
        cancelButton.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        cancelButton.setPreferredSize(new Dimension(80, 30));
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // 主面板
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        
        // 标题
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.gridwidth = 2;
        gbc.anchor = GridBagConstraints.CENTER;
        JLabel titleLabel = new JLabel("考试时长配置");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 16));
        mainPanel.add(titleLabel, gbc);
        
        // 说明文字
        gbc.gridy = 1;
        JLabel descLabel = new JLabel("设置所有学生考试的默认时长");
        descLabel.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        descLabel.setForeground(Color.GRAY);
        mainPanel.add(descLabel, gbc);
        
        // 考试时长
        gbc.gridy = 2;
        gbc.gridwidth = 1;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel durationLabel = new JLabel("考试时长:");
        durationLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        mainPanel.add(durationLabel, gbc);
        
        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        JPanel durationPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        durationPanel.add(examDurationSpinner);
        JLabel unitLabel = new JLabel(" 分钟 (5-180分钟)");
        unitLabel.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        unitLabel.setForeground(Color.GRAY);
        durationPanel.add(unitLabel);
        mainPanel.add(durationPanel, gbc);
        
        add(mainPanel, BorderLayout.CENTER);
        
        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        buttonPanel.add(confirmButton);
        buttonPanel.add(cancelButton);
        
        add(buttonPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        confirmButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (validateInput()) {
                    examDurationMinutes = (Integer) examDurationSpinner.getValue();
                    confirmed = true;
                    dispose();
                }
            }
        });
        
        cancelButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                confirmed = false;
                dispose();
            }
        });
        
        // ESC键取消
        KeyStroke escapeKeyStroke = KeyStroke.getKeyStroke("ESCAPE");
        getRootPane().getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW).put(escapeKeyStroke, "ESCAPE");
        getRootPane().getActionMap().put("ESCAPE", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                confirmed = false;
                dispose();
            }
        });
        
        // Enter键确认
        getRootPane().setDefaultButton(confirmButton);
    }
    
    /**
     * 设置对话框属性
     */
    private void setupDialog() {
        setSize(400, 250);
        setLocationRelativeTo(getParent());
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
        setResizable(false);
    }
    
    /**
     * 验证输入
     */
    private boolean validateInput() {
        int duration = (Integer) examDurationSpinner.getValue();
        
        if (duration < 5 || duration > 180) {
            JOptionPane.showMessageDialog(this,
                "考试时长必须在5-180分钟之间",
                "输入错误", JOptionPane.WARNING_MESSAGE);
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查是否确认
     */
    public boolean isConfirmed() {
        return confirmed;
    }
    
    /**
     * 获取考试时长（分钟）
     */
    public int getExamDurationMinutes() {
        return examDurationMinutes;
    }
}
