<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6889fdc0-46f5-4a75-9c95-4af65b4b4841" name="Changes" comment="init">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/exam/client/ui/LoginFrame.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/exam/client/ui/LoginFrame.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\Env\apache-maven-3.9.9" />
        <option name="localRepository" value="E:\Env\apache-maven-3.9.9\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\Env\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2yMvBg4MJXhHLAGcrjnGrkcidY6" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.ExamClient.executor": "Debug",
    "Application.ExamServer.executor": "Debug",
    "Maven.exam-system [clean].executor": "Run",
    "Maven.exam-system [install].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "ignore_missing_gitignore": "true",
    "last_opened_file_path": "F:/Win/BR/PartTime/2.Processing/6.16 考试系统CW 168/exam-system/lib",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Libraries",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "reference.settings.project.maven.importing",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="F:\Win\BR\PartTime\2.Processing\6.16 考试系统CW 168\exam-system\lib" />
      <recent name="F:\Win\BR\PartTime\2.Processing\6.16 考试系统CW 168\exam-system" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="F:\Win\BR\PartTime\2.Processing\6.16 考试系统CW 168\exam-system" />
    </key>
  </component>
  <component name="RunManager" selected="Application.ExamClient">
    <configuration name="ExamClient" type="Application" factoryName="Application" singleton="false" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.exam.client.ExamClient" />
      <module name="exam-system" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.exam.client.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ExamServer" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.exam.server.ExamServer" />
      <module name="exam-system" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.exam.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.ExamClient" />
      <item itemvalue="Application.ExamServer" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Application.ExamClient" />
        <item itemvalue="Application.ExamServer" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="6889fdc0-46f5-4a75-9c95-4af65b4b4841" name="Changes" comment="" />
      <created>1746638827820</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746638827820</updated>
      <workItem from="1749657123652" duration="17511000" />
    </task>
    <task id="LOCAL-00001" summary="init">
      <option name="closed" value="true" />
      <created>1749710477228</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1749710477228</updated>
    </task>
    <task id="LOCAL-00002" summary="init">
      <option name="closed" value="true" />
      <created>1749710631845</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1749710631845</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="LOCAL_CHANGES_DETAILS_PREVIEW_SHOWN" value="false" />
    <MESSAGE value="init" />
    <option name="LAST_COMMIT_MESSAGE" value="init" />
  </component>
</project>