package com.exam.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * 考试记录实体类
 * 实现MVC模式中的Model层
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ExamRecord implements Serializable {
    private static final long serialVersionUID = 1L;
    
    // 考试状态枚举
    public enum ExamStatus {
        COMPLETED("COMPLETED", "已完成"),
        TIMEOUT("TIMEOUT", "超时"),
        INTERRUPTED("INTERRUPTED", "中断");
        
        private final String code;
        private final String description;
        
        ExamStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() { return code; }
        public String getDescription() { return description; }
    }
    
    // 考试记录属性
    private Integer recordId;          // 记录编号（主键）
    private Integer userId;            // 用户编号
    private String userName;           // 用户姓名
    private Timestamp examTime;        // 考试时间
    private Integer totalQuestions;    // 总题数
    private Integer correctAnswers;    // 正确答案数
    private BigDecimal score;          // 考试成绩
    private Integer timeUsed;          // 用时（秒）
    private Integer examDuration;      // 考试时长（秒）
    private ExamStatus status;         // 考试状态
    
    // 考试详情列表（一对多关系）
    private List<ExamDetail> examDetails;
    
    // 默认构造函数
    public ExamRecord() {
        this.totalQuestions = 0;
        this.correctAnswers = 0;
        this.score = BigDecimal.ZERO;
        this.timeUsed = 0;
        this.examDuration = 1800; // 默认30分钟
        this.status = ExamStatus.COMPLETED;
        this.examDetails = new ArrayList<>();
    }
    
    // 带参构造函数
    public ExamRecord(Integer userId, String userName, Integer totalQuestions, Integer examDuration) {
        this();
        this.userId = userId;
        this.userName = userName;
        this.totalQuestions = totalQuestions;
        this.examDuration = examDuration;
        this.examTime = new Timestamp(System.currentTimeMillis());
    }
    
    // Getter和Setter方法
    public Integer getRecordId() {
        return recordId;
    }
    
    public void setRecordId(Integer recordId) {
        this.recordId = recordId;
    }
    
    public Integer getUserId() {
        return userId;
    }
    
    public void setUserId(Integer userId) {
        this.userId = userId;
    }
    
    public String getUserName() {
        return userName;
    }
    
    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    public Timestamp getExamTime() {
        return examTime;
    }
    
    public void setExamTime(Timestamp examTime) {
        this.examTime = examTime;
    }
    
    public Integer getTotalQuestions() {
        return totalQuestions;
    }
    
    public void setTotalQuestions(Integer totalQuestions) {
        this.totalQuestions = totalQuestions;
    }
    
    public Integer getCorrectAnswers() {
        return correctAnswers;
    }
    
    public void setCorrectAnswers(Integer correctAnswers) {
        this.correctAnswers = correctAnswers;
    }
    
    public BigDecimal getScore() {
        return score;
    }
    
    public void setScore(BigDecimal score) {
        this.score = score;
    }
    
    public Integer getTimeUsed() {
        return timeUsed;
    }
    
    public void setTimeUsed(Integer timeUsed) {
        this.timeUsed = timeUsed;
    }
    
    public Integer getExamDuration() {
        return examDuration;
    }
    
    public void setExamDuration(Integer examDuration) {
        this.examDuration = examDuration;
    }
    
    public ExamStatus getStatus() {
        return status;
    }
    
    public void setStatus(ExamStatus status) {
        this.status = status;
    }
    
    public List<ExamDetail> getExamDetails() {
        return examDetails;
    }
    
    public void setExamDetails(List<ExamDetail> examDetails) {
        this.examDetails = examDetails;
    }
    
    // 业务方法
    
    /**
     * 计算考试成绩
     * 成绩 = (正确答案数 / 总题数) * 100
     */
    public void calculateScore() {
        if (totalQuestions > 0) {
            double scoreValue = (double) correctAnswers / totalQuestions * 100;
            this.score = BigDecimal.valueOf(scoreValue).setScale(2, BigDecimal.ROUND_HALF_UP);
        } else {
            this.score = BigDecimal.ZERO;
        }
    }
    
    /**
     * 添加考试详情
     * @param detail 考试详情
     */
    public void addExamDetail(ExamDetail detail) {
        if (examDetails == null) {
            examDetails = new ArrayList<>();
        }
        examDetails.add(detail);
        
        // 更新统计信息
        if (detail.isCorrect()) {
            correctAnswers++;
        }
        calculateScore();
    }
    
    /**
     * 检查是否通过考试
     * @param passScore 及格分数
     * @return true如果通过，否则false
     */
    public boolean isPassed(double passScore) {
        return score.doubleValue() >= passScore;
    }
    
    /**
     * 获取正确率
     * @return 正确率（百分比）
     */
    public double getAccuracy() {
        if (totalQuestions > 0) {
            return (double) correctAnswers / totalQuestions * 100;
        }
        return 0.0;
    }
    
    /**
     * 获取剩余时间（秒）
     * @return 剩余时间
     */
    public int getRemainingTime() {
        return Math.max(0, examDuration - timeUsed);
    }
    
    /**
     * 检查是否超时
     * @return true如果超时，否则false
     */
    public boolean isTimeout() {
        return timeUsed >= examDuration;
    }
    
    /**
     * 格式化考试时长
     * @return 格式化的时长字符串（如：30分00秒）
     */
    public String getFormattedDuration() {
        int minutes = timeUsed / 60;
        int seconds = timeUsed % 60;
        return String.format("%d分%02d秒", minutes, seconds);
    }
    
    /**
     * 获取成绩等级
     * @return 成绩等级字符串
     */
    public String getGradeLevel() {
        double scoreValue = score.doubleValue();
        if (scoreValue >= 90) return "优秀";
        else if (scoreValue >= 80) return "良好";
        else if (scoreValue >= 70) return "中等";
        else if (scoreValue >= 60) return "及格";
        else return "不及格";
    }
    
    @Override
    public String toString() {
        return "ExamRecord{" +
                "recordId=" + recordId +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                ", examTime=" + examTime +
                ", totalQuestions=" + totalQuestions +
                ", correctAnswers=" + correctAnswers +
                ", score=" + score +
                ", timeUsed=" + timeUsed +
                ", status=" + status +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ExamRecord that = (ExamRecord) obj;
        return recordId != null && recordId.equals(that.recordId);
    }
    
    @Override
    public int hashCode() {
        return recordId != null ? recordId.hashCode() : 0;
    }
}
