package com.exam.util;

import java.io.*;
import java.util.Properties;

/**
 * 考试配置管理类
 * 用于管理考试系统的配置参数
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ExamConfig {
    private static final String CONFIG_FILE = "exam-config.properties";
    private static final String DEFAULT_EXAM_DURATION_KEY = "exam.duration.minutes";
    private static final int DEFAULT_EXAM_DURATION = 30; // 默认30分钟
    
    private static ExamConfig instance;
    private Properties properties;
    
    /**
     * 私有构造函数
     */
    private ExamConfig() {
        properties = new Properties();
        loadConfig();
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized ExamConfig getInstance() {
        if (instance == null) {
            instance = new ExamConfig();
        }
        return instance;
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        File configFile = new File(CONFIG_FILE);
        if (configFile.exists()) {
            try (FileInputStream fis = new FileInputStream(configFile)) {
                properties.load(fis);
                System.out.println("考试配置加载成功");
            } catch (IOException e) {
                System.err.println("加载考试配置失败: " + e.getMessage());
                setDefaultValues();
            }
        } else {
            System.out.println("配置文件不存在，使用默认配置");
            setDefaultValues();
            saveConfig();
        }
    }
    
    /**
     * 设置默认值
     */
    private void setDefaultValues() {
        properties.setProperty(DEFAULT_EXAM_DURATION_KEY, String.valueOf(DEFAULT_EXAM_DURATION));
    }
    
    /**
     * 保存配置文件
     */
    private void saveConfig() {
        try (FileOutputStream fos = new FileOutputStream(CONFIG_FILE)) {
            properties.store(fos, "Exam System Configuration");
            System.out.println("考试配置保存成功");
        } catch (IOException e) {
            System.err.println("保存考试配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取默认考试时长（分钟）
     */
    public int getDefaultExamDurationMinutes() {
        String value = properties.getProperty(DEFAULT_EXAM_DURATION_KEY, String.valueOf(DEFAULT_EXAM_DURATION));
        try {
            int duration = Integer.parseInt(value);
            // 验证范围
            if (duration < 5 || duration > 180) {
                System.err.println("考试时长配置超出范围，使用默认值: " + DEFAULT_EXAM_DURATION);
                return DEFAULT_EXAM_DURATION;
            }
            return duration;
        } catch (NumberFormatException e) {
            System.err.println("考试时长配置格式错误，使用默认值: " + DEFAULT_EXAM_DURATION);
            return DEFAULT_EXAM_DURATION;
        }
    }
    
    /**
     * 设置默认考试时长（分钟）
     */
    public void setDefaultExamDurationMinutes(int minutes) {
        if (minutes < 5 || minutes > 180) {
            throw new IllegalArgumentException("考试时长必须在5-180分钟之间");
        }
        properties.setProperty(DEFAULT_EXAM_DURATION_KEY, String.valueOf(minutes));
        saveConfig();
    }
    
    /**
     * 获取默认考试时长（秒）
     */
    public int getDefaultExamDurationSeconds() {
        return getDefaultExamDurationMinutes() * 60;
    }
    
    /**
     * 重新加载配置
     */
    public void reload() {
        loadConfig();
    }
    
    /**
     * 获取配置信息字符串
     */
    public String getConfigInfo() {
        return String.format("默认考试时长: %d 分钟", getDefaultExamDurationMinutes());
    }
}
