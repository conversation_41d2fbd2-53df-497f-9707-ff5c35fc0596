package com.exam.client.ui;

import com.exam.client.ExamClient;
import com.exam.model.Message;
import com.exam.model.User;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;

/**
 * 登录界面
 * 实现MVC模式中的View层
 * 提供用户登录功能界面
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class LoginFrame extends JFrame {
    
    private ExamClient client;
    
    // UI组件
    private JTextField usernameField;
    private JPasswordField passwordField;
    private JButton loginButton;
    private JButton registerButton;
    private JButton exitButton;
    private JLabel statusLabel;
    
    /**
     * 构造函数
     * @param client 客户端对象
     */
    public LoginFrame(ExamClient client) {
        this.client = client;
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        setupFrame();
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeComponents() {
        // 创建输入字段
        usernameField = new JTextField(20);
        passwordField = new JPasswordField(20);
        
        // 创建按钮
        loginButton = new JButton("登录");
        registerButton = new JButton("注册");
        exitButton = new JButton("退出");
        
        // 创建状态标签
        statusLabel = new JLabel("请输入用户名和密码");
        statusLabel.setForeground(Color.BLUE);
        
        // 设置按钮样式
        loginButton.setPreferredSize(new Dimension(80, 30));
        registerButton.setPreferredSize(new Dimension(80, 30));
        exitButton.setPreferredSize(new Dimension(80, 30));
        
        // 设置默认按钮
        getRootPane().setDefaultButton(loginButton);
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // 创建主面板
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // 标题
        JLabel titleLabel = new JLabel("考试系统登录");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 24));
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(titleLabel, gbc);

        // 用户名标签和输入框
        gbc.gridwidth = 1;
        gbc.gridy = 2;
        gbc.gridx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        mainPanel.add(new JLabel("用户名:"), gbc);
        
        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(usernameField, gbc);
        
        // 密码标签和输入框
        gbc.gridy = 3;
        gbc.gridx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        gbc.fill = GridBagConstraints.NONE;
        mainPanel.add(new JLabel("密码:"), gbc);
        
        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(passwordField, gbc);
        
        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.add(loginButton);
        buttonPanel.add(registerButton);
        buttonPanel.add(exitButton);
        
        gbc.gridy = 4;
        gbc.gridx = 0;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(buttonPanel, gbc);
        
        // 状态标签
        gbc.gridy = 5;
        gbc.anchor = GridBagConstraints.CENTER;
        mainPanel.add(statusLabel, gbc);
        
        add(mainPanel, BorderLayout.CENTER);
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 登录按钮事件
        loginButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                performLogin();
            }
        });
        
        // 注册按钮事件
        registerButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                showRegisterDialog();
            }
        });
        
        // 退出按钮事件
        exitButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                exitApplication();
            }
        });
        
        // 回车键登录
        KeyAdapter enterKeyListener = new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    performLogin();
                }
            }
        };
        
        usernameField.addKeyListener(enterKeyListener);
        passwordField.addKeyListener(enterKeyListener);
        
        // 窗口关闭事件
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        addWindowListener(new java.awt.event.WindowAdapter() {
            @Override
            public void windowClosing(java.awt.event.WindowEvent e) {
                exitApplication();
            }
        });
    }
    
    /**
     * 设置窗口属性
     */
    private void setupFrame() {
        setTitle("考试系统 - 登录");
        setSize(400, 350);
        setLocationRelativeTo(null);
        setResizable(false);
        
        // 设置图标（如果有的话）
        try {
            // 这里可以设置应用程序图标
            // setIconImage(ImageIO.read(getClass().getResource("/icon.png")));
        } catch (Exception e) {
            // 忽略图标加载失败
        }
    }
    
    /**
     * 执行登录操作
     */
    private void performLogin() {
        String username = usernameField.getText().trim();
        String password = new String(passwordField.getPassword());
        
        // 验证输入
        if (username.isEmpty()) {
            showStatus("请输入用户名", Color.RED);
            usernameField.requestFocus();
            return;
        }
        
        if (password.isEmpty()) {
            showStatus("请输入密码", Color.RED);
            passwordField.requestFocus();
            return;
        }
        
        // 禁用按钮，显示登录中状态
        setButtonsEnabled(false);
        showStatus("正在登录...", Color.BLUE);
        
        // 在后台线程中执行登录
        SwingWorker<Message, Void> loginWorker = new SwingWorker<Message, Void>() {
            @Override
            protected Message doInBackground() throws Exception {
                return client.login(username, password);
            }
            
            @Override
            protected void done() {
                try {
                    Message response = get();
                    handleLoginResponse(response);
                } catch (Exception e) {
                    showStatus("登录失败: " + e.getMessage(), Color.RED);
                } finally {
                    setButtonsEnabled(true);
                }
            }
        };
        
        loginWorker.execute();
    }
    
    /**
     * 处理登录响应
     * @param response 服务器响应
     */
    private void handleLoginResponse(Message response) {
        if (response.isSuccess()) {
            showStatus("登录成功", Color.GREEN);
            
            // 启动心跳线程
            client.startHeartbeat();
            
            // 根据用户类型打开相应界面
            User user = client.getCurrentUser();
            if (user.isAdmin()) {
                // 打开管理员界面
                SwingUtilities.invokeLater(() -> {
                    AdminFrame adminFrame = new AdminFrame(client);
                    adminFrame.setVisible(true);
                    dispose();
                });
            } else {
                // 打开学生界面
                SwingUtilities.invokeLater(() -> {
                    StudentFrame studentFrame = new StudentFrame(client);
                    studentFrame.setVisible(true);
                    dispose();
                });
            }
        } else {
            showStatus("登录失败: " + response.getMessage(), Color.RED);
            passwordField.setText("");
            passwordField.requestFocus();
        }
    }
    
    /**
     * 显示注册对话框
     */
    private void showRegisterDialog() {
        RegisterFrame registerFrame = new RegisterFrame(this, client);
        registerFrame.setVisible(true);
    }
    
    /**
     * 退出应用程序
     */
    private void exitApplication() {
        int option = JOptionPane.showConfirmDialog(
            this,
            "确定要退出考试系统吗？",
            "确认退出",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.QUESTION_MESSAGE
        );
        
        if (option == JOptionPane.YES_OPTION) {
            client.disconnect();
            System.exit(0);
        }
    }
    
    /**
     * 显示状态信息
     * @param message 状态消息
     * @param color 文字颜色
     */
    private void showStatus(String message, Color color) {
        statusLabel.setText(message);
        statusLabel.setForeground(color);
    }
    
    /**
     * 设置按钮启用状态
     * @param enabled 是否启用
     */
    private void setButtonsEnabled(boolean enabled) {
        loginButton.setEnabled(enabled);
        registerButton.setEnabled(enabled);
        exitButton.setEnabled(enabled);
        usernameField.setEnabled(enabled);
        passwordField.setEnabled(enabled);
    }
    
    /**
     * 重置登录表单
     */
    public void resetForm() {
        usernameField.setText("");
        passwordField.setText("");
        showStatus("请输入用户名和密码", Color.BLUE);
        usernameField.requestFocus();
    }
}
