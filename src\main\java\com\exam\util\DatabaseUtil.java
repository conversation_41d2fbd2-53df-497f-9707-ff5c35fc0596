package com.exam.util;

import java.io.IOException;
import java.io.InputStream;
import java.sql.*;
import java.util.Properties;

/**
 * 数据库工具类
 * 提供数据库连接管理和基本操作
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class DatabaseUtil {
    
    // 数据库连接配置
    private static String DRIVER;
    private static String URL;
    private static String USERNAME;
    private static String PASSWORD;
    
    // 静态初始化块，加载数据库配置
    static {
        loadDatabaseConfig();
    }
    
    /**
     * 加载数据库配置文件
     */
    private static void loadDatabaseConfig() {
        Properties props = new Properties();
        InputStream input = null;
        
        try {
            // 从类路径加载配置文件
            input = DatabaseUtil.class.getClassLoader()
                    .getResourceAsStream("config/database.properties");
            
            if (input == null) {
                System.err.println("无法找到数据库配置文件 database.properties");
                return;
            }
            
            // 加载配置属性
            props.load(input);
            
            DRIVER = props.getProperty("db.driver", "com.mysql.cj.jdbc.Driver");
            URL = props.getProperty("db.url", 
                "********************************************************************************************************");
            USERNAME = props.getProperty("db.username", "root");
            PASSWORD = props.getProperty("db.password", "root");
            
            // 加载数据库驱动
            Class.forName(DRIVER);
            
            System.out.println("数据库配置加载成功");
            
        } catch (IOException e) {
            System.err.println("加载数据库配置文件失败: " + e.getMessage());
        } catch (ClassNotFoundException e) {
            System.err.println("数据库驱动加载失败: " + e.getMessage());
        } finally {
            if (input != null) {
                try {
                    input.close();
                } catch (IOException e) {
                    System.err.println("关闭配置文件流失败: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 获取数据库连接
     * @return 数据库连接对象
     * @throws SQLException 数据库连接异常
     */
    public static Connection getConnection() throws SQLException {
        try {
            Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            // 设置自动提交为false，支持事务
            conn.setAutoCommit(false);
            return conn;
        } catch (SQLException e) {
            System.err.println("获取数据库连接失败: " + e.getMessage());
            System.err.println("数据库URL: " + URL);
            System.err.println("用户名: " + USERNAME);
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 测试数据库连接
     * @return true如果连接成功，否则false
     */
    public static boolean testConnection() {
        Connection conn = null;
        try {
            System.out.println("开始测试数据库连接...");
            conn = getConnection();
            boolean isValid = conn != null && !conn.isClosed();
            if (isValid) {
                System.out.println("数据库连接测试成功");
            } else {
                System.err.println("数据库连接测试失败：连接无效");
            }
            return isValid;
        } catch (SQLException e) {
            System.err.println("数据库连接测试失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            closeConnection(conn);
        }
    }

    /**
     * 关闭数据库连接
     * @param conn 数据库连接
     */
    public static void closeConnection(Connection conn) {
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                System.err.println("关闭数据库连接失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 关闭PreparedStatement
     * @param pstmt PreparedStatement对象
     */
    public static void closePreparedStatement(PreparedStatement pstmt) {
        if (pstmt != null) {
            try {
                pstmt.close();
            } catch (SQLException e) {
                System.err.println("关闭PreparedStatement失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 关闭ResultSet
     * @param rs ResultSet对象
     */
    public static void closeResultSet(ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                System.err.println("关闭ResultSet失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 关闭所有数据库资源
     * @param conn 数据库连接
     * @param pstmt PreparedStatement对象
     * @param rs ResultSet对象
     */
    public static void closeAll(Connection conn, PreparedStatement pstmt, ResultSet rs) {
        closeResultSet(rs);
        closePreparedStatement(pstmt);
        closeConnection(conn);
    }
    
    /**
     * 提交事务
     * @param conn 数据库连接
     */
    public static void commit(Connection conn) {
        if (conn != null) {
            try {
                conn.commit();
            } catch (SQLException e) {
                System.err.println("提交事务失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 回滚事务
     * @param conn 数据库连接
     */
    public static void rollback(Connection conn) {
        if (conn != null) {
            try {
                conn.rollback();
            } catch (SQLException e) {
                System.err.println("回滚事务失败: " + e.getMessage());
            }
        }
    }
    /**
     * 获取数据库配置信息（用于调试）
     * @return 配置信息字符串
     */
    public static String getDatabaseInfo() {
        return "Database Info: " +
               "Driver=" + DRIVER +
               ", URL=" + URL +
               ", Username=" + USERNAME;
    }
}
