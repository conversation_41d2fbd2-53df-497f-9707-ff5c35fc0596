package com.exam.client.ui;

import com.exam.client.ExamClient;
import com.exam.model.Message;
import com.exam.model.User;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * 用户注册界面
 * 实现MVC模式中的View层
 * 提供用户注册功能界面
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class RegisterFrame extends JDialog {
    
    private LoginFrame parentFrame;
    private ExamClient client;
    
    // UI组件
    private JTextField usernameField;
    private JPasswordField passwordField;
    private JPasswordField confirmPasswordField;
    private JTextField realNameField;
    private JTextField studentIdField;
    private JTextField emailField;
    private JTextField phoneField;
    private JComboBox<String> userTypeCombo;
    private JButton registerButton;
    private JButton cancelButton;
    private JLabel statusLabel;
    
    /**
     * 构造函数
     * @param parent 父窗口
     * @param client 客户端对象
     */
    public RegisterFrame(LoginFrame parent, ExamClient client) {
        super(parent, "用户注册", true);
        this.parentFrame = parent;
        this.client = client;
        
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        setupDialog();
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeComponents() {
        // 创建输入字段
        usernameField = new JTextField(20);
        passwordField = new JPasswordField(20);
        confirmPasswordField = new JPasswordField(20);
        realNameField = new JTextField(20);
        studentIdField = new JTextField(20);
        studentIdField.setText("3240608005"); // 预设学号
        emailField = new JTextField(20);
        phoneField = new JTextField(20);
        
        // 创建用户类型下拉框
        userTypeCombo = new JComboBox<>(new String[]{"学生", "管理员"});
        userTypeCombo.setSelectedIndex(0); // 默认选择学生
        
        // 创建按钮
        registerButton = new JButton("注册");
        cancelButton = new JButton("取消");
        
        // 创建状态标签
        statusLabel = new JLabel("请填写注册信息");
        statusLabel.setForeground(Color.BLUE);
        
        // 设置按钮样式
        registerButton.setPreferredSize(new Dimension(80, 30));
        cancelButton.setPreferredSize(new Dimension(80, 30));
        
        // 设置默认按钮
        getRootPane().setDefaultButton(registerButton);
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // 创建主面板
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // 标题
        JLabel titleLabel = new JLabel("用户注册");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 18));
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(titleLabel, gbc);
        
        // 表单字段
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.NONE;
        
        // 用户名
        gbc.gridy = 1;
        gbc.gridx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        mainPanel.add(new JLabel("用户名:"), gbc);
        
        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(usernameField, gbc);
        
        // 密码
        gbc.gridy = 2;
        gbc.gridx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        gbc.fill = GridBagConstraints.NONE;
        mainPanel.add(new JLabel("密码:"), gbc);
        
        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(passwordField, gbc);
        
        // 确认密码
        gbc.gridy = 3;
        gbc.gridx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        gbc.fill = GridBagConstraints.NONE;
        mainPanel.add(new JLabel("确认密码:"), gbc);
        
        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(confirmPasswordField, gbc);
        
        // 真实姓名
        gbc.gridy = 4;
        gbc.gridx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        gbc.fill = GridBagConstraints.NONE;
        mainPanel.add(new JLabel("真实姓名:"), gbc);

        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(realNameField, gbc);

        // 学号（仅学生）
        gbc.gridy = 5;
        gbc.gridx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        gbc.fill = GridBagConstraints.NONE;
        mainPanel.add(new JLabel("学号:"), gbc);

        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(studentIdField, gbc);

        // 邮箱
        gbc.gridy = 6;
        gbc.gridx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        gbc.fill = GridBagConstraints.NONE;
        mainPanel.add(new JLabel("邮箱:"), gbc);
        
        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(emailField, gbc);
        
        // 电话
        gbc.gridy = 7;
        gbc.gridx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        gbc.fill = GridBagConstraints.NONE;
        mainPanel.add(new JLabel("电话:"), gbc);

        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(phoneField, gbc);

        // 用户类型
        gbc.gridy = 8;
        gbc.gridx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        gbc.fill = GridBagConstraints.NONE;
        mainPanel.add(new JLabel("用户类型:"), gbc);

        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(userTypeCombo, gbc);

        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.add(registerButton);
        buttonPanel.add(cancelButton);

        gbc.gridy = 9;
        gbc.gridx = 0;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(buttonPanel, gbc);

        // 状态标签
        gbc.gridy = 10;
        gbc.anchor = GridBagConstraints.CENTER;
        mainPanel.add(statusLabel, gbc);
        
        add(mainPanel, BorderLayout.CENTER);
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 注册按钮事件
        registerButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                performRegister();
            }
        });
        
        // 取消按钮事件
        cancelButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dispose();
            }
        });
    }
    
    /**
     * 设置对话框属性
     */
    private void setupDialog() {
        setSize(400, 500);
        setLocationRelativeTo(parentFrame);
        setResizable(false);
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
    }
    
    /**
     * 执行注册操作
     */
    private void performRegister() {
        // 获取输入数据
        String username = usernameField.getText().trim();
        String password = new String(passwordField.getPassword());
        String confirmPassword = new String(confirmPasswordField.getPassword());
        String realName = realNameField.getText().trim();
        String studentId = studentIdField.getText().trim();
        String email = emailField.getText().trim();
        String phone = phoneField.getText().trim();
        String userTypeStr = (String) userTypeCombo.getSelectedItem();

        // 验证输入
        if (!validateInput(username, password, confirmPassword, realName, studentId, userTypeStr)) {
            return;
        }

        // 创建用户对象
        User user = new User();
        user.setUsername(username);
        user.setPassword(password);
        user.setRealName(realName);
        user.setStudentId(studentId);
        user.setEmail(email);
        user.setPhone(phone);
        user.setUserType("学生".equals(userTypeStr) ? User.UserType.STUDENT : User.UserType.ADMIN);
        
        // 禁用按钮，显示注册中状态
        setButtonsEnabled(false);
        showStatus("正在注册...", Color.BLUE);
        
        // 在后台线程中执行注册
        SwingWorker<Message, Void> registerWorker = new SwingWorker<Message, Void>() {
            @Override
            protected Message doInBackground() throws Exception {
                return client.register(user);
            }
            
            @Override
            protected void done() {
                try {
                    Message response = get();
                    handleRegisterResponse(response);
                } catch (Exception e) {
                    showStatus("注册失败: " + e.getMessage(), Color.RED);
                } finally {
                    setButtonsEnabled(true);
                }
            }
        };
        
        registerWorker.execute();
    }
    
    /**
     * 验证输入数据
     */
    private boolean validateInput(String username, String password, String confirmPassword, String realName, String studentId, String userTypeStr) {
        if (username.isEmpty()) {
            showStatus("请输入用户名", Color.RED);
            usernameField.requestFocus();
            return false;
        }

        if (username.length() < 3) {
            showStatus("用户名至少3个字符", Color.RED);
            usernameField.requestFocus();
            return false;
        }

        if (password.isEmpty()) {
            showStatus("请输入密码", Color.RED);
            passwordField.requestFocus();
            return false;
        }

        if (password.length() < 6) {
            showStatus("密码至少6个字符", Color.RED);
            passwordField.requestFocus();
            return false;
        }

        if (!password.equals(confirmPassword)) {
            showStatus("两次输入的密码不一致", Color.RED);
            confirmPasswordField.requestFocus();
            return false;
        }

        if (realName.isEmpty()) {
            showStatus("请输入真实姓名", Color.RED);
            realNameField.requestFocus();
            return false;
        }

        // 如果是学生，验证学号
        if ("学生".equals(userTypeStr)) {
            if (studentId.isEmpty()) {
                showStatus("学生用户请输入学号", Color.RED);
                studentIdField.requestFocus();
                return false;
            }

            if (studentId.length() != 10) {
                showStatus("学号应为10位数字", Color.RED);
                studentIdField.requestFocus();
                return false;
            }
        }

        return true;
    }
    
    /**
     * 处理注册响应
     */
    private void handleRegisterResponse(Message response) {
        if (response.isSuccess()) {
            showStatus("注册成功", Color.GREEN);
            JOptionPane.showMessageDialog(this, "注册成功！请使用新账户登录。", "注册成功", JOptionPane.INFORMATION_MESSAGE);
            dispose();
        } else {
            showStatus("注册失败: " + response.getMessage(), Color.RED);
        }
    }
    
    /**
     * 显示状态信息
     */
    private void showStatus(String message, Color color) {
        statusLabel.setText(message);
        statusLabel.setForeground(color);
    }
    
    /**
     * 设置按钮启用状态
     */
    private void setButtonsEnabled(boolean enabled) {
        registerButton.setEnabled(enabled);
        cancelButton.setEnabled(enabled);
    }
}
