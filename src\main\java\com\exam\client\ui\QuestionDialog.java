package com.exam.client.ui;

import com.exam.model.Question;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * 题目编辑对话框
 * 用于添加和编辑题目
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class QuestionDialog extends JDialog {
    
    private Question question;
    private boolean isEditMode;
    private boolean confirmed = false;
    
    // UI组件
    private JTextArea questionContentArea;
    private JTextField optionAField;
    private JTextField optionBField;
    private JTextField optionCField;
    private JTextField optionDField;
    private JComboBox<Question.Answer> correctAnswerCombo;
    private JComboBox<Question.Difficulty> difficultyCombo;
    private JTextField subjectField;
    private JButton confirmButton;
    private JButton cancelButton;
    
    /**
     * 构造函数 - 添加模式
     * @param parent 父窗口
     */
    public QuestionDialog(JFrame parent) {
        this(parent, null);
    }
    
    /**
     * 构造函数 - 编辑模式
     * @param parent 父窗口
     * @param question 要编辑的题目
     */
    public QuestionDialog(JFrame parent, Question question) {
        super(parent, question == null ? "添加题目" : "编辑题目", true);
        this.question = question;
        this.isEditMode = (question != null);
        
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        setupDialog();
        
        if (isEditMode) {
            loadQuestionData();
        }
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeComponents() {
        // 题目内容
        questionContentArea = new JTextArea(4, 40);
        questionContentArea.setLineWrap(true);
        questionContentArea.setWrapStyleWord(true);
        questionContentArea.setBorder(BorderFactory.createLoweredBevelBorder());
        
        // 选项输入框
        optionAField = new JTextField(30);
        optionBField = new JTextField(30);
        optionCField = new JTextField(30);
        optionDField = new JTextField(30);
        
        // 正确答案下拉框
        correctAnswerCombo = new JComboBox<>(Question.Answer.values());
        
        // 难度等级下拉框 - 显示中文
        difficultyCombo = new JComboBox<>(Question.Difficulty.values());
        difficultyCombo.setSelectedItem(Question.Difficulty.MEDIUM);
        // 设置自定义渲染器显示中文描述
        difficultyCombo.setRenderer(new DefaultListCellRenderer() {
            @Override
            public Component getListCellRendererComponent(JList<?> list, Object value,
                    int index, boolean isSelected, boolean cellHasFocus) {
                super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
                if (value instanceof Question.Difficulty) {
                    setText(((Question.Difficulty) value).getDescription());
                }
                return this;
            }
        });
        
        // 科目输入框
        subjectField = new JTextField("计算机基础", 20);
        
        // 按钮
        confirmButton = new JButton(isEditMode ? "更新" : "添加");
        cancelButton = new JButton("取消");
        
        // 设置按钮样式
        confirmButton.setPreferredSize(new Dimension(80, 30));
        cancelButton.setPreferredSize(new Dimension(80, 30));
        
        // 设置默认按钮
        getRootPane().setDefaultButton(confirmButton);
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // 主面板
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // 题目内容
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.anchor = GridBagConstraints.NORTHWEST;
        mainPanel.add(new JLabel("题目内容:"), gbc);
        
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 0.3;
        JScrollPane questionScrollPane = new JScrollPane(questionContentArea);
        mainPanel.add(questionScrollPane, gbc);
        
        // 选项A
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        gbc.weighty = 0;
        gbc.anchor = GridBagConstraints.EAST;
        mainPanel.add(new JLabel("选项A:"), gbc);
        
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        gbc.anchor = GridBagConstraints.WEST;
        mainPanel.add(optionAField, gbc);
        
        // 选项B
        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        mainPanel.add(new JLabel("选项B:"), gbc);
        
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        gbc.anchor = GridBagConstraints.WEST;
        mainPanel.add(optionBField, gbc);
        
        // 选项C
        gbc.gridx = 0;
        gbc.gridy = 3;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        mainPanel.add(new JLabel("选项C:"), gbc);
        
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        gbc.anchor = GridBagConstraints.WEST;
        mainPanel.add(optionCField, gbc);
        
        // 选项D
        gbc.gridx = 0;
        gbc.gridy = 4;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        mainPanel.add(new JLabel("选项D:"), gbc);
        
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        gbc.anchor = GridBagConstraints.WEST;
        mainPanel.add(optionDField, gbc);
        
        // 正确答案
        gbc.gridx = 0;
        gbc.gridy = 5;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        mainPanel.add(new JLabel("正确答案:"), gbc);
        
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        gbc.anchor = GridBagConstraints.WEST;
        mainPanel.add(correctAnswerCombo, gbc);
        
        // 难度等级
        gbc.gridx = 0;
        gbc.gridy = 6;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        mainPanel.add(new JLabel("难度等级:"), gbc);
        
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        gbc.anchor = GridBagConstraints.WEST;
        mainPanel.add(difficultyCombo, gbc);
        
        // 科目
        gbc.gridx = 0;
        gbc.gridy = 7;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        mainPanel.add(new JLabel("科目:"), gbc);
        
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        gbc.anchor = GridBagConstraints.WEST;
        mainPanel.add(subjectField, gbc);
        
        add(mainPanel, BorderLayout.CENTER);
        
        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.add(confirmButton);
        buttonPanel.add(cancelButton);
        
        add(buttonPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 确认按钮事件
        confirmButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (validateInput()) {
                    saveQuestionData();
                    confirmed = true;
                    dispose();
                }
            }
        });
        
        // 取消按钮事件
        cancelButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                confirmed = false;
                dispose();
            }
        });
        
        // 窗口关闭事件
        setDefaultCloseOperation(JDialog.DO_NOTHING_ON_CLOSE);
        addWindowListener(new java.awt.event.WindowAdapter() {
            @Override
            public void windowClosing(java.awt.event.WindowEvent e) {
                confirmed = false;
                dispose();
            }
        });
    }
    
    /**
     * 设置对话框属性
     */
    private void setupDialog() {
        setSize(600, 500);
        setLocationRelativeTo(getParent());
        setResizable(false);
    }
    
    /**
     * 加载题目数据（编辑模式）
     */
    private void loadQuestionData() {
        if (question != null) {
            questionContentArea.setText(question.getQuestionContent());
            optionAField.setText(question.getOptionA());
            optionBField.setText(question.getOptionB());
            optionCField.setText(question.getOptionC());
            optionDField.setText(question.getOptionD());
            correctAnswerCombo.setSelectedItem(question.getCorrectAnswer());
            difficultyCombo.setSelectedItem(question.getDifficulty());
            subjectField.setText(question.getSubject());
        }
    }
    
    /**
     * 保存题目数据
     */
    private void saveQuestionData() {
        if (question == null) {
            question = new Question();
        }
        
        question.setQuestionContent(questionContentArea.getText().trim());
        question.setOptionA(optionAField.getText().trim());
        question.setOptionB(optionBField.getText().trim());
        question.setOptionC(optionCField.getText().trim());
        question.setOptionD(optionDField.getText().trim());
        question.setCorrectAnswer((Question.Answer) correctAnswerCombo.getSelectedItem());
        question.setDifficulty((Question.Difficulty) difficultyCombo.getSelectedItem());
        question.setSubject(subjectField.getText().trim());
    }
    
    /**
     * 验证输入
     */
    private boolean validateInput() {
        // 验证题目内容
        if (questionContentArea.getText().trim().isEmpty()) {
            showError("请输入题目内容");
            questionContentArea.requestFocus();
            return false;
        }
        
        // 验证选项A
        if (optionAField.getText().trim().isEmpty()) {
            showError("请输入选项A");
            optionAField.requestFocus();
            return false;
        }
        
        // 验证选项B
        if (optionBField.getText().trim().isEmpty()) {
            showError("请输入选项B");
            optionBField.requestFocus();
            return false;
        }
        
        // 验证选项C
        if (optionCField.getText().trim().isEmpty()) {
            showError("请输入选项C");
            optionCField.requestFocus();
            return false;
        }
        
        // 验证选项D
        if (optionDField.getText().trim().isEmpty()) {
            showError("请输入选项D");
            optionDField.requestFocus();
            return false;
        }
        
        // 验证科目
        if (subjectField.getText().trim().isEmpty()) {
            showError("请输入科目");
            subjectField.requestFocus();
            return false;
        }
        
        return true;
    }
    
    /**
     * 显示错误信息
     */
    private void showError(String message) {
        JOptionPane.showMessageDialog(this, message, "输入错误", JOptionPane.ERROR_MESSAGE);
    }
    
    /**
     * 获取题目对象
     */
    public Question getQuestion() {
        return question;
    }
    
    /**
     * 检查是否确认
     */
    public boolean isConfirmed() {
        return confirmed;
    }
}
